# AI编程工具 - 使用指南

## 🚀 项目概述

这是一个基于Web的AI编程工具，复刻了Cursor编辑器的核心功能。项目提供了完整的代码编辑、智能搜索和AI交互体验。

### 核心功能

- 🖥️ **Monaco编辑器**: 支持语法高亮、自动补全、错误检测
- 📁 **虚拟文件系统**: 内存中的文件管理，支持导入真实项目
- 🔍 **智能代码搜索**: 基于语义的代码符号搜索
- 🤖 **AI助手集成**: 与OpenAI API集成，支持上下文对话
- ⚡ **实时协作**: WebSocket支持多用户协作
- 🔧 **性能优化**: 针对大型项目的性能调优

## 📦 安装和环境配置

### 后端环境

1. **Python依赖安装**:
```bash
pip install -r requirements.txt
```

2. **可选依赖** (用于增强功能):
```bash
# 语义搜索功能
pip install sentence-transformers faiss-cpu

# tree-sitter代码解析
pip install tree-sitter tree-sitter-python tree-sitter-javascript tree-sitter-typescript

# AI功能
pip install openai
```

3. **环境变量配置**:
```bash
# 设置OpenAI API密钥
export OPENAI_API_KEY="your-api-key-here"
```

### 前端环境

1. **安装Node.js依赖**:
```bash
cd frontend
npm install
```

2. **主要依赖包括**:
- React 18 + TypeScript
- Monaco Editor
- Tailwind CSS
- Lucide React (图标)

## 🏃‍♂️ 运行项目

### 开发模式

1. **启动后端服务**:
```bash
python -m uvicorn app.api_service:app --reload --host 0.0.0.0 --port 8000
```

2. **启动前端开发服务器**:
```bash
cd frontend
npm run dev
```

3. **访问应用**:
打开浏览器访问 `http://localhost:3000`

### 生产模式

1. **构建前端**:
```bash
cd frontend
npm run build
```

2. **启动生产服务**:
```bash
python -m uvicorn app.api_service:app --host 0.0.0.0 --port 8000
```

### Docker部署

```bash
docker-compose up -d
```

## 📖 使用教程

### 基本操作

1. **导入项目**:
   - 通过API: `POST /api/workspace/import`
   - 或使用性能优化脚本导入

2. **文件操作**:
   - 在左侧文件树中浏览项目结构
   - 右键菜单支持创建、删除、重命名
   - 点击文件在中央编辑器中打开

3. **代码编辑**:
   - 支持语法高亮和自动补全
   - Ctrl+S 保存文件
   - 支持多种编程语言

4. **AI助手**:
   - 在右侧聊天面板输入问题
   - 绿色按钮发送时会包含当前文件上下文
   - 支持代码解释、错误检查、优化建议

### 高级功能

#### 语义搜索

```bash
# 重建搜索索引
curl -X POST http://localhost:8000/api/search/rebuild_index

# 语义搜索
curl -X POST http://localhost:8000/api/search/semantic \
  -H "Content-Type: application/json" \
  -d '{"query": "data processing function", "max_results": 10}'
```

#### 上下文构建

```bash
# 构建代码上下文
curl -X POST http://localhost:8000/api/search/context \
  -H "Content-Type: application/json" \
  -d '{
    "query": "main function execution",
    "focus_files": ["/main.py"],
    "max_files": 10,
    "max_symbols": 50
  }'
```

#### AI对话

```bash
# 与AI助手对话
curl -X POST http://localhost:8000/api/ai/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "请解释这段代码的作用",
    "model": "gpt-3.5-turbo"
  }'
```

## 🔧 性能优化

### 运行性能测试

```bash
# 完整性能基准测试
python scripts/performance_optimizer.py

# 指定项目路径
python scripts/performance_optimizer.py --project /path/to/your/project

# 仅运行内存优化
python scripts/performance_optimizer.py --memory-only
```

### 性能调优建议

1. **大型项目优化**:
   - 启用增量索引更新
   - 使用分片处理大文件
   - 配置合适的缓存策略

2. **内存管理**:
   - 定期运行内存优化
   - 监控符号缓存大小
   - 使用压缩存储

3. **搜索性能**:
   - 调整embedding模型大小
   - 优化索引参数
   - 使用并行搜索

## 🧪 测试

### 运行测试套件

```bash
# 运行所有测试
pytest tests/ -v

# 运行特定测试
pytest tests/test_context_engine.py -v
pytest tests/test_api_integration.py -v

# 生成覆盖率报告
pytest tests/ --cov=app --cov-report=html
```

### 手动测试

1. **健康检查**:
```bash
curl http://localhost:8000/health
```

2. **API测试**:
```bash
# 获取文件树
curl http://localhost:8000/api/files/tree

# 获取工作空间信息
curl http://localhost:8000/api/workspace/info
```

## 🔧 配置选项

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `OPENAI_API_KEY` | OpenAI API密钥 | 无 |
| `SEMANTIC_MODEL` | 语义搜索模型名称 | `all-MiniLM-L6-v2` |
| `MAX_FILE_SIZE` | 最大文件大小(MB) | `10` |
| `CACHE_SIZE` | 缓存大小限制 | `1000` |

### 配置文件

创建 `config.json` 自定义配置:

```json
{
  "api": {
    "host": "0.0.0.0",
    "port": 8000,
    "cors_origins": ["http://localhost:3000"]
  },
  "ai": {
    "default_model": "gpt-3.5-turbo",
    "max_tokens": 2000,
    "temperature": 0.7
  },
  "search": {
    "model_name": "all-MiniLM-L6-v2",
    "index_dimension": 384,
    "max_results": 50
  },
  "performance": {
    "enable_caching": true,
    "cache_ttl": 3600,
    "max_symbols": 100000
  }
}
```

## 🚨 故障排除

### 常见问题

1. **后端服务无法启动**:
   - 检查Python依赖是否安装完整
   - 确认端口8000未被占用
   - 查看日志输出定位错误

2. **前端无法连接后端**:
   - 确认后端服务正在运行
   - 检查CORS配置
   - 验证API路径是否正确

3. **AI功能不可用**:
   - 确认OPENAI_API_KEY已设置
   - 检查API密钥是否有效
   - 验证网络连接

4. **语义搜索不工作**:
   - 安装sentence-transformers依赖
   - 下载模型文件
   - 重建搜索索引

5. **性能问题**:
   - 运行性能优化脚本
   - 调整缓存配置
   - 使用分片处理大项目

### 日志调试

启用详细日志:

```bash
# 设置日志级别
export LOG_LEVEL=DEBUG

# 启动服务
python -m uvicorn app.api_service:app --log-level debug
```

查看实时日志:

```bash
# 使用tail查看日志
tail -f logs/app.log

# 使用journalctl (systemd)
journalctl -f -u ai-coding-tool
```

## 📚 API文档

启动服务后访问自动生成的API文档:

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 🤝 贡献指南

1. **代码风格**:
   - Python: 遵循PEP 8
   - TypeScript: 使用Prettier格式化
   - 提交前运行linter检查

2. **测试要求**:
   - 新功能必须包含测试
   - 保持测试覆盖率>80%
   - 运行完整测试套件

3. **文档更新**:
   - 更新相关文档
   - 添加使用示例
   - 更新变更日志

## 📋 版本历史

### v0.1.0 (当前版本)
- ✅ 基础虚拟文件系统
- ✅ Monaco编辑器集成
- ✅ 代码解析和语义搜索
- ✅ AI助手集成
- ✅ 性能优化工具
- ✅ 完整测试套件

### 未来计划
- 🔄 增量索引更新
- 🎨 主题和插件系统
- 📊 代码分析仪表板
- 🔀 Git集成
- 🌐 多语言支持

## 📄 许可证

本项目基于MIT许可证开源。详见 `LICENSE` 文件。

## 📞 支持

如有问题或建议，请通过以下方式联系:

- 📧 Email: <EMAIL>
- 💬 GitHub Issues: [项目地址](https://github.com/your-org/ai-coding-tool)
- 📖 文档: [在线文档](https://docs.ai-coding-tool.com)

---

**Happy Coding! 🚀** 