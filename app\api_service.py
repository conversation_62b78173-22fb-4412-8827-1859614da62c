"""
AI编程工具 - 后端API服务

提供RESTful API接口，连接虚拟文件系统、上下文引擎、AI服务等核心模块。
支持文件管理、代码搜索、AI交互、实时协作等功能。

主要端点：
- /api/files/* - 文件系统操作
- /api/search/* - 代码搜索和分析
- /api/ai/* - AI交互服务
- /api/workspace/* - 工作空间管理
"""

import os
import json
import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, UploadFile, File, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
import uvicorn

try:
    import openai
    from openai import AsyncOpenAI
except ImportError:
    print("Warning: OpenAI library not available")
    openai = None
    AsyncOpenAI = None

from .vfs import VirtualFileSystem, FileType, FileNode
from .context_engine import CodeParser, SemanticSearcher, ContextBuilder, CodeContext, CodeSymbol

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
vfs: Optional[VirtualFileSystem] = None
code_parser: Optional[CodeParser] = None
semantic_searcher: Optional[SemanticSearcher] = None
context_builder: Optional[ContextBuilder] = None
ai_client: Optional[AsyncOpenAI] = None
connected_clients: Dict[str, WebSocket] = {}


# Pydantic模型定义
class FileOperationRequest(BaseModel):
    path: str
    content: Optional[str] = None
    encoding: str = "utf-8"
    recursive: bool = False


class FileOperationResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None


class SearchRequest(BaseModel):
    query: str
    file_path: Optional[str] = None
    max_results: int = 20
    min_score: float = 0.5


class SearchResult(BaseModel):
    symbol_name: str
    symbol_type: str
    file_path: str
    line_start: int
    line_end: int
    definition: str
    documentation: Optional[str] = None
    score: float


class SearchResponse(BaseModel):
    query: str
    results: List[SearchResult]
    total_count: int
    search_time: float


class ContextRequest(BaseModel):
    query: str
    focus_files: Optional[List[str]] = None
    max_files: int = 10
    max_symbols: int = 50


class ContextResponse(BaseModel):
    primary_file: str
    primary_symbols: List[SearchResult]
    related_files: List[str]
    related_symbols: List[SearchResult]
    imports: List[str]
    exports: List[str]
    dependencies: Dict[str, List[str]]
    context_score: float


class AIRequest(BaseModel):
    message: str
    context: Optional[ContextResponse] = None
    conversation_id: Optional[str] = None
    model: str = "gpt-3.5-turbo"


class AIResponse(BaseModel):
    response: str
    conversation_id: str
    model: str
    usage: Optional[Dict[str, Any]] = None


class FileTreeNode(BaseModel):
    name: str
    path: str
    type: str  # "file" or "directory"
    size: Optional[int] = None
    modified_at: Optional[datetime] = None
    children: Optional[List['FileTreeNode']] = None


class WorkspaceInfo(BaseModel):
    root_path: str
    total_files: int
    total_directories: int
    total_size: int
    languages: List[str]
    last_modified: datetime


# API应用初始化
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    await initialize_services()
    yield
    # 关闭时清理
    await cleanup_services()


app = FastAPI(
    title="AI编程工具 API",
    description="基于Web的AI编程工具后端服务",
    version="0.1.0",
    lifespan=lifespan
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


async def initialize_services():
    """初始化所有服务"""
    global vfs, code_parser, semantic_searcher, context_builder, ai_client
    
    logger.info("Initializing services...")
    
    # 初始化虚拟文件系统
    vfs = VirtualFileSystem()
    
    # 初始化代码解析器
    code_parser = CodeParser()
    
    # 初始化语义搜索器
    semantic_searcher = SemanticSearcher()
    
    # 初始化上下文构建器
    context_builder = ContextBuilder(vfs, code_parser, semantic_searcher)
    
    # 初始化AI客户端
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key and AsyncOpenAI:
        ai_client = AsyncOpenAI(api_key=api_key)
        logger.info("OpenAI client initialized")
    else:
        logger.warning("OpenAI API key not found or client not available")
    
    logger.info("Services initialized successfully")


async def cleanup_services():
    """清理服务资源"""
    logger.info("Cleaning up services...")
    # 这里可以添加资源清理逻辑


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "vfs": vfs is not None,
            "parser": code_parser is not None,
            "searcher": semantic_searcher is not None,
            "context_builder": context_builder is not None,
            "ai_client": ai_client is not None,
        }
    }


# 文件系统API
@app.get("/api/files/tree")
async def get_file_tree(path: str = "/") -> List[FileTreeNode]:
    """获取文件树结构"""
    if not vfs:
        raise HTTPException(status_code=503, detail="VFS not initialized")
    
    try:
        node = vfs.resolve_path(path)
        if not node:
            raise HTTPException(status_code=404, detail="Path not found")
        
        def build_tree_node(file_node: FileNode) -> FileTreeNode:
            tree_node = FileTreeNode(
                name=file_node.name,
                path=file_node.path,
                type="directory" if file_node.is_directory else "file",
                size=file_node.metadata.size if file_node.is_file else None,
                modified_at=file_node.metadata.modified_at
            )
            
            if file_node.is_directory:
                tree_node.children = [
                    build_tree_node(child) 
                    for child in file_node.children.values()
                ]
                tree_node.children.sort(key=lambda x: (x.type == "file", x.name))
            
            return tree_node
        
        if node.is_directory:
            return [build_tree_node(child) for child in node.children.values()]
        else:
            return [build_tree_node(node)]
    
    except Exception as e:
        logger.error(f"Error getting file tree: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/files/content")
async def get_file_content(path: str, encoding: str = "utf-8") -> Dict[str, Any]:
    """获取文件内容"""
    if not vfs:
        raise HTTPException(status_code=503, detail="VFS not initialized")
    
    try:
        if not vfs.exists(path):
            raise HTTPException(status_code=404, detail="File not found")
        
        if not vfs.is_file(path):
            raise HTTPException(status_code=400, detail="Path is not a file")
        
        content = vfs.read_file(path, encoding=encoding)
        node = vfs.resolve_path(path)
        
        return {
            "path": path,
            "content": content,
            "size": node.metadata.size if node else 0,
            "modified_at": node.metadata.modified_at.isoformat() if node else None,
            "language": code_parser.detect_language(path, content).value if code_parser else "unknown"
        }
    
    except Exception as e:
        logger.error(f"Error reading file {path}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/files/content")
async def update_file_content(request: FileOperationRequest) -> FileOperationResponse:
    """更新文件内容"""
    if not vfs:
        raise HTTPException(status_code=503, detail="VFS not initialized")
    
    try:
        success = vfs.write_file(request.path, request.content or "", request.encoding)
        
        if success:
            # 更新语义搜索索引
            await update_search_index(request.path)
            
            return FileOperationResponse(
                success=True,
                message=f"File {request.path} updated successfully"
            )
        else:
            return FileOperationResponse(
                success=False,
                message=f"Failed to update file {request.path}"
            )
    
    except Exception as e:
        logger.error(f"Error updating file {request.path}: {e}")
        return FileOperationResponse(
            success=False,
            message=str(e)
        )


@app.post("/api/files/create")
async def create_file(request: FileOperationRequest) -> FileOperationResponse:
    """创建文件或目录"""
    if not vfs:
        raise HTTPException(status_code=503, detail="VFS not initialized")
    
    try:
        if request.content is not None:
            # 创建文件
            success = vfs.touch(request.path, request.content)
            message = f"File {request.path} created successfully"
        else:
            # 创建目录
            success = vfs.mkdir(request.path, parents=True)
            message = f"Directory {request.path} created successfully"
        
        if success:
            # 更新搜索索引
            if request.content is not None:
                await update_search_index(request.path)
            
            return FileOperationResponse(success=True, message=message)
        else:
            return FileOperationResponse(
                success=False,
                message=f"Failed to create {request.path}"
            )
    
    except Exception as e:
        logger.error(f"Error creating {request.path}: {e}")
        return FileOperationResponse(success=False, message=str(e))


@app.delete("/api/files/delete")
async def delete_file(path: str, recursive: bool = False) -> FileOperationResponse:
    """删除文件或目录"""
    if not vfs:
        raise HTTPException(status_code=503, detail="VFS not initialized")
    
    try:
        success = vfs.remove(path, recursive=recursive)
        
        if success:
            return FileOperationResponse(
                success=True,
                message=f"Successfully deleted {path}"
            )
        else:
            return FileOperationResponse(
                success=False,
                message=f"Failed to delete {path}"
            )
    
    except Exception as e:
        logger.error(f"Error deleting {path}: {e}")
        return FileOperationResponse(success=False, message=str(e))


# 搜索和分析API
@app.post("/api/search/semantic")
async def semantic_search(request: SearchRequest) -> SearchResponse:
    """语义搜索代码符号"""
    if not semantic_searcher:
        raise HTTPException(status_code=503, detail="Semantic searcher not initialized")
    
    start_time = datetime.now()
    
    try:
        results = semantic_searcher.search(
            request.query,
            top_k=request.max_results,
            min_score=request.min_score
        )
        
        search_results = []
        for symbol, score in results:
            search_results.append(SearchResult(
                symbol_name=symbol.name,
                symbol_type=symbol.symbol_type.value,
                file_path=symbol.file_path,
                line_start=symbol.line_start,
                line_end=symbol.line_end,
                definition=symbol.definition,
                documentation=symbol.documentation,
                score=score
            ))
        
        search_time = (datetime.now() - start_time).total_seconds()
        
        return SearchResponse(
            query=request.query,
            results=search_results,
            total_count=len(search_results),
            search_time=search_time
        )
    
    except Exception as e:
        logger.error(f"Semantic search error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/search/context")
async def build_context(request: ContextRequest) -> ContextResponse:
    """构建代码上下文"""
    if not context_builder:
        raise HTTPException(status_code=503, detail="Context builder not initialized")
    
    try:
        context = context_builder.build_context(
            query=request.query,
            focus_files=request.focus_files,
            max_files=request.max_files,
            max_symbols=request.max_symbols
        )
        
        def symbol_to_result(symbol: CodeSymbol) -> SearchResult:
            return SearchResult(
                symbol_name=symbol.name,
                symbol_type=symbol.symbol_type.value,
                file_path=symbol.file_path,
                line_start=symbol.line_start,
                line_end=symbol.line_end,
                definition=symbol.definition,
                documentation=symbol.documentation,
                score=1.0  # 上下文中的符号默认高分
            )
        
        return ContextResponse(
            primary_file=context.primary_file,
            primary_symbols=[symbol_to_result(s) for s in context.primary_symbols],
            related_files=context.related_files,
            related_symbols=[symbol_to_result(s) for s in context.related_symbols],
            imports=context.imports,
            exports=context.exports,
            dependencies=context.dependencies,
            context_score=context.context_score
        )
    
    except Exception as e:
        logger.error(f"Context building error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/search/rebuild_index")
async def rebuild_search_index() -> FileOperationResponse:
    """重建搜索索引"""
    if not semantic_searcher or not code_parser or not vfs:
        raise HTTPException(status_code=503, detail="Required services not initialized")
    
    try:
        # 收集所有代码文件的符号
        all_symbols = []
        
        for file_path in vfs.walk():
            if vfs.is_file(file_path[0]):
                try:
                    content = vfs.read_file(file_path[0], encoding='utf-8')
                    if isinstance(content, bytes):
                        content = content.decode('utf-8')
                    
                    symbols = code_parser.extract_symbols(file_path[0], content)
                    all_symbols.extend(symbols)
                except Exception as e:
                    logger.warning(f"Failed to process {file_path[0]}: {e}")
        
        # 构建搜索索引
        semantic_searcher.build_index(all_symbols)
        
        return FileOperationResponse(
            success=True,
            message=f"Search index rebuilt with {len(all_symbols)} symbols"
        )
    
    except Exception as e:
        logger.error(f"Index rebuild error: {e}")
        return FileOperationResponse(success=False, message=str(e))


# AI交互API
@app.post("/api/ai/chat")
async def chat_with_ai(request: AIRequest) -> AIResponse:
    """与AI助手对话"""
    if not ai_client:
        raise HTTPException(status_code=503, detail="AI client not initialized")
    
    try:
        # 构建对话消息
        messages = [
            {
                "role": "system",
                "content": "你是一个AI编程助手，专门帮助开发者理解和编写代码。请提供准确、实用的编程建议。"
            }
        ]
        
        # 添加上下文信息
        if request.context:
            context_info = f"""
当前代码上下文：
主要文件: {request.context.primary_file}
相关文件: {', '.join(request.context.related_files[:5])}
主要符号: {', '.join([s.symbol_name for s in request.context.primary_symbols[:10]])}
"""
            messages.append({
                "role": "system",
                "content": context_info
            })
        
        messages.append({
            "role": "user",
            "content": request.message
        })
        
        # 调用OpenAI API
        response = await ai_client.chat.completions.create(
            model=request.model,
            messages=messages,
            max_tokens=2000,
            temperature=0.7
        )
        
        return AIResponse(
            response=response.choices[0].message.content,
            conversation_id=request.conversation_id or "default",
            model=request.model,
            usage={
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            } if response.usage else None
        )
    
    except Exception as e:
        logger.error(f"AI chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 工作空间管理API
@app.get("/api/workspace/info")
async def get_workspace_info() -> WorkspaceInfo:
    """获取工作空间信息"""
    if not vfs:
        raise HTTPException(status_code=503, detail="VFS not initialized")
    
    try:
        stats = vfs.get_stats()
        
        # 统计语言类型
        languages = set()
        if code_parser:
            for file_path in vfs.walk():
                if vfs.is_file(file_path[0]):
                    lang = code_parser.detect_language(file_path[0])
                    if lang.value != "unknown":
                        languages.add(lang.value)
        
        return WorkspaceInfo(
            root_path="/",
            total_files=stats.get("file_count", 0),
            total_directories=stats.get("directory_count", 0),
            total_size=stats.get("total_size", 0),
            languages=list(languages),
            last_modified=datetime.now()  # 简化实现
        )
    
    except Exception as e:
        logger.error(f"Workspace info error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/workspace/import")
async def import_from_filesystem(
    real_path: str,
    virtual_path: str = "/"
) -> FileOperationResponse:
    """从真实文件系统导入"""
    if not vfs:
        raise HTTPException(status_code=503, detail="VFS not initialized")
    
    try:
        success = vfs.import_from_filesystem(real_path, virtual_path)
        
        if success:
            # 重建搜索索引
            await rebuild_search_index()
            
            return FileOperationResponse(
                success=True,
                message=f"Successfully imported {real_path} to {virtual_path}"
            )
        else:
            return FileOperationResponse(
                success=False,
                message=f"Failed to import {real_path}"
            )
    
    except Exception as e:
        logger.error(f"Import error: {e}")
        return FileOperationResponse(success=False, message=str(e))


# WebSocket支持
@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket连接处理"""
    await websocket.accept()
    connected_clients[client_id] = websocket
    
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # 处理不同类型的消息
            if message.get("type") == "ping":
                await websocket.send_text(json.dumps({"type": "pong"}))
            elif message.get("type") == "file_change":
                # 广播文件变更
                await broadcast_file_change(message, client_id)
            
    except WebSocketDisconnect:
        logger.info(f"Client {client_id} disconnected")
    finally:
        connected_clients.pop(client_id, None)


async def broadcast_file_change(message: Dict[str, Any], sender_id: str):
    """广播文件变更给其他客户端"""
    for client_id, websocket in connected_clients.items():
        if client_id != sender_id:
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Failed to send message to {client_id}: {e}")


# 辅助函数
async def update_search_index(file_path: str):
    """更新单个文件的搜索索引"""
    if not semantic_searcher or not code_parser or not vfs:
        return
    
    try:
        content = vfs.read_file(file_path, encoding='utf-8')
        if isinstance(content, bytes):
            content = content.decode('utf-8')
        
        symbols = code_parser.extract_symbols(file_path, content)
        
        # 这里应该实现增量索引更新，简化实现为重建整个索引
        # 在实际应用中，需要更高效的增量更新机制
        logger.info(f"Updated search index for {file_path} with {len(symbols)} symbols")
        
    except Exception as e:
        logger.error(f"Failed to update search index for {file_path}: {e}")


# 静态文件服务（生产环境）
if os.path.exists("frontend/dist"):
    app.mount("/", StaticFiles(directory="frontend/dist", html=True), name="static")


if __name__ == "__main__":
    # 开发环境运行
    uvicorn.run(
        "app.api_service:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=["app"]
    ) 