"""
上下文引擎测试

测试代码解析、语义搜索和上下文构建功能
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from datetime import datetime

from app.context_engine import (
    CodeParser, SemanticSearcher, ContextBuilder,
    LanguageType, SymbolType, CodeSymbol, CodeContext
)
from app.vfs import VirtualFileSystem


class TestCodeParser:
    """代码解析器测试"""
    
    def setup_method(self):
        """测试前的设置"""
        self.parser = CodeParser()
    
    def test_detect_language_by_extension(self):
        """测试基于文件扩展名的语言检测"""
        test_cases = [
            ('test.py', LanguageType.PYTHON),
            ('app.js', LanguageType.JAVASCRIPT),
            ('component.jsx', LanguageType.JSX),
            ('service.ts', LanguageType.TYPESCRIPT),
            ('component.tsx', LanguageType.TSX),
            ('config.json', LanguageType.JSON),
            ('readme.md', LanguageType.MARKDOWN),
            ('unknown.xyz', LanguageType.UNKNOWN),
        ]
        
        for file_path, expected_lang in test_cases:
            detected = self.parser.detect_language(file_path)
            assert detected == expected_lang, f"Expected {expected_lang}, got {detected} for {file_path}"
    
    def test_detect_language_by_content(self):
        """测试基于内容的语言检测"""
        # Python内容
        python_content = """
def hello_world():
    print("Hello, World!")
    
class MyClass:
    pass
"""
        assert self.parser.detect_language('unknown.txt', python_content) == LanguageType.PYTHON
        
        # JavaScript内容
        js_content = """
function helloWorld() {
    console.log("Hello, World!");
}

const myVar = 42;
"""
        assert self.parser.detect_language('unknown.txt', js_content) == LanguageType.JAVASCRIPT
        
        # TypeScript内容
        ts_content = """
interface User {
    name: string;
    age: number;
}

function greet(user: User): string {
    return `Hello, ${user.name}!`;
}
"""
        assert self.parser.detect_language('unknown.txt', ts_content) == LanguageType.TYPESCRIPT
    
    def test_extract_symbols_python_regex(self):
        """测试Python符号提取（正则表达式方式）"""
        python_code = '''
import os
from typing import List

def calculate_sum(numbers: List[int]) -> int:
    """计算数字列表的和"""
    return sum(numbers)

class Calculator:
    """简单计算器类"""
    
    def __init__(self):
        self.history = []
    
    def add(self, a: int, b: int) -> int:
        """加法运算"""
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
'''
        
        symbols = self.parser.extract_symbols('test.py', python_code)
        
        # 验证提取的符号
        function_symbols = [s for s in symbols if s.symbol_type == SymbolType.FUNCTION]
        class_symbols = [s for s in symbols if s.symbol_type == SymbolType.CLASS]
        import_symbols = [s for s in symbols if s.symbol_type == SymbolType.IMPORT]
        
        assert len(function_symbols) >= 1  # calculate_sum
        assert len(class_symbols) >= 1     # Calculator
        assert len(import_symbols) >= 2    # import statements
        
        # 验证函数符号
        calc_func = next((s for s in function_symbols if s.name == 'calculate_sum'), None)
        assert calc_func is not None
        assert calc_func.line_start > 0
        assert 'def calculate_sum' in calc_func.definition
        
        # 验证类符号  
        calc_class = next((s for s in class_symbols if s.name == 'Calculator'), None)
        assert calc_class is not None
        assert calc_class.line_start > 0
        assert 'class Calculator' in calc_class.definition
    
    def test_extract_symbols_javascript_regex(self):
        """测试JavaScript符号提取（正则表达式方式）"""
        js_code = '''
const express = require('express');
import { Router } from 'express';

function createServer(port) {
    const app = express();
    return app;
}

class ApiServer {
    constructor(config) {
        this.config = config;
    }
    
    start() {
        console.log('Server starting...');
    }
}

const config = {
    port: 3000,
    host: 'localhost'
};
'''
        
        symbols = self.parser.extract_symbols('test.js', js_code)
        
        # 应该提取到函数、类等符号
        assert len(symbols) > 0
        
        # 验证有函数符号
        function_symbols = [s for s in symbols if s.symbol_type == SymbolType.FUNCTION]
        assert len(function_symbols) > 0


class TestSemanticSearcher:
    """语义搜索器测试"""
    
    def setup_method(self):
        """测试前的设置"""
        self.searcher = SemanticSearcher()
    
    @pytest.mark.skipif(
        not hasattr(self.searcher, 'model') or self.searcher.model is None,
        reason="Semantic search model not available"
    )
    def test_build_index(self):
        """测试构建搜索索引"""
        # 创建测试符号
        symbols = [
            CodeSymbol(
                name="calculate_sum",
                symbol_type=SymbolType.FUNCTION,
                language=LanguageType.PYTHON,
                file_path="math_utils.py",
                line_start=5,
                line_end=8,
                column_start=0,
                column_end=20,
                definition="def calculate_sum(numbers):\n    return sum(numbers)",
                documentation="计算数字列表的和"
            ),
            CodeSymbol(
                name="Calculator",
                symbol_type=SymbolType.CLASS,
                language=LanguageType.PYTHON,
                file_path="calculator.py", 
                line_start=10,
                line_end=25,
                column_start=0,
                column_end=15,
                definition="class Calculator:\n    def __init__(self):\n        pass",
                documentation="简单计算器类"
            )
        ]
        
        # 构建索引
        self.searcher.build_index(symbols)
        
        # 验证索引构建成功
        assert self.searcher.index is not None
        assert len(self.searcher.symbols) == 2
        assert self.searcher.embeddings is not None
    
    @pytest.mark.skipif(
        not hasattr(SemanticSearcher(), 'model') or SemanticSearcher().model is None,
        reason="Semantic search model not available"
    )
    def test_semantic_search(self):
        """测试语义搜索"""
        # 准备测试数据
        symbols = [
            CodeSymbol(
                name="add_numbers",
                symbol_type=SymbolType.FUNCTION,
                language=LanguageType.PYTHON,
                file_path="math.py",
                line_start=1,
                line_end=3,
                column_start=0,
                column_end=20,
                definition="def add_numbers(a, b):\n    return a + b",
                documentation="Add two numbers together"
            ),
            CodeSymbol(
                name="multiply",
                symbol_type=SymbolType.FUNCTION,
                language=LanguageType.PYTHON,
                file_path="math.py",
                line_start=5,
                line_end=7,
                column_start=0,
                column_end=20,
                definition="def multiply(a, b):\n    return a * b",
                documentation="Multiply two numbers"
            )
        ]
        
        self.searcher.build_index(symbols)
        
        # 执行搜索
        results = self.searcher.search("addition function", top_k=5, min_score=0.1)
        
        # 验证搜索结果
        assert len(results) > 0
        assert all(isinstance(result, tuple) and len(result) == 2 for result in results)
        assert all(isinstance(score, float) for _, score in results)


class TestContextBuilder:
    """上下文构建器测试"""
    
    def setup_method(self):
        """测试前的设置"""
        self.vfs = VirtualFileSystem()
        self.parser = CodeParser()
        self.searcher = SemanticSearcher()
        self.context_builder = ContextBuilder(self.vfs, self.parser, self.searcher)
        
        # 创建测试文件
        self._setup_test_files()
    
    def _setup_test_files(self):
        """设置测试文件"""
        # Python文件
        python_code = '''
from typing import List

def process_data(data: List[str]) -> List[str]:
    """处理数据列表"""
    return [item.strip().upper() for item in data]

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.cache = {}
    
    def transform(self, data):
        return process_data(data)
'''
        
        self.vfs.touch('/src/processor.py', python_code)
        
        # JavaScript文件
        js_code = '''
const { DataProcessor } = require('./processor');

function formatData(data) {
    return data.map(item => item.toLowerCase());
}

class DataFormatter {
    constructor() {
        this.processor = new DataProcessor();
    }
    
    format(data) {
        return formatData(data);
    }
}

module.exports = { DataFormatter };
'''
        
        self.vfs.touch('/src/formatter.js', js_code)
    
    def test_get_file_symbols(self):
        """测试获取文件符号"""
        symbols = self.context_builder._get_file_symbols('/src/processor.py')
        
        assert len(symbols) > 0
        
        # 验证包含函数和类符号
        function_symbols = [s for s in symbols if s.symbol_type == SymbolType.FUNCTION]
        class_symbols = [s for s in symbols if s.symbol_type == SymbolType.CLASS]
        
        assert len(function_symbols) > 0
        assert len(class_symbols) > 0
    
    def test_get_file_dependencies(self):
        """测试获取文件依赖关系"""
        deps = self.context_builder._get_file_dependencies('/src/processor.py')
        
        # Python文件应该有typing依赖
        assert isinstance(deps, set)
        # 由于是模拟环境，依赖解析可能为空，这是正常的
    
    def test_build_context_basic(self):
        """测试基本上下文构建"""
        # 由于语义搜索可能不可用，这里主要测试结构
        context = self.context_builder.build_context(
            query="data processing",
            focus_files=['/src/processor.py'],
            max_files=5,
            max_symbols=20
        )
        
        assert isinstance(context, CodeContext)
        assert isinstance(context.primary_symbols, list)
        assert isinstance(context.related_files, list)
        assert isinstance(context.related_symbols, list)
        assert isinstance(context.imports, list)
        assert isinstance(context.exports, list)
        assert isinstance(context.dependencies, dict)
        assert isinstance(context.context_score, float)


class TestIntegration:
    """集成测试"""
    
    def setup_method(self):
        """测试前的设置"""
        self.vfs = VirtualFileSystem()
        self.parser = CodeParser()
        self.searcher = SemanticSearcher()
        self.context_builder = ContextBuilder(self.vfs, self.parser, self.searcher)
    
    def test_full_workflow(self):
        """测试完整工作流程"""
        # 1. 创建测试项目
        project_files = {
            '/main.py': '''
from utils import helper

def main():
    """主函数"""
    result = helper.process_data(["hello", "world"])
    print(result)

if __name__ == "__main__":
    main()
''',
            '/utils/helper.py': '''
def process_data(items):
    """处理数据项"""
    return [item.upper() for item in items]

def validate_input(data):
    """验证输入数据"""
    return isinstance(data, list) and all(isinstance(item, str) for item in data)
'''
        }
        
        # 创建文件
        for path, content in project_files.items():
            self.vfs.touch(path, content)
        
        # 2. 解析所有文件的符号
        all_symbols = []
        for path in project_files.keys():
            content = self.vfs.read_file(path, encoding='utf-8')
            if isinstance(content, bytes):
                content = content.decode('utf-8')
            symbols = self.parser.extract_symbols(path, content)
            all_symbols.extend(symbols)
        
        # 3. 验证符号提取
        assert len(all_symbols) > 0
        
        function_symbols = [s for s in all_symbols if s.symbol_type == SymbolType.FUNCTION]
        assert len(function_symbols) >= 3  # main, process_data, validate_input
        
        # 4. 构建语义搜索索引（如果可用）
        if self.searcher.model is not None:
            self.searcher.build_index(all_symbols)
            
            # 5. 执行语义搜索
            results = self.searcher.search("data processing function", top_k=5)
            assert isinstance(results, list)
        
        # 6. 构建上下文
        context = self.context_builder.build_context(
            query="main function execution",
            focus_files=['/main.py'],
            max_files=5,
            max_symbols=10
        )
        
        # 验证上下文结构
        assert isinstance(context, CodeContext)
        assert len(context.primary_file) > 0
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试不存在的文件
        symbols = self.context_builder._get_file_symbols('/nonexistent.py')
        assert symbols == []
        
        # 测试空查询
        context = self.context_builder.build_context("", max_files=1, max_symbols=1)
        assert isinstance(context, CodeContext)


if __name__ == '__main__':
    pytest.main([__file__, '-v']) 