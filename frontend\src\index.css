@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern AI IDE Global Styles */
:root {
  /* Dark Theme Colors - VSCode/Cursor/Windsurf Style */
  --bg-primary: #1e1e1e;
  --bg-secondary: #252526;
  --bg-tertiary: #2d2d30;
  --bg-quaternary: #37373d;
  
  --border-primary: #3e3e42;
  --border-secondary: #484848;
  
  --text-primary: #cccccc;
  --text-secondary: #9d9d9d;
  --text-tertiary: #6a6a6a;
  
  --accent-blue: #007acc;
  --accent-blue-hover: #1177bb;
  --accent-green: #4caf50;
  --accent-orange: #ff9800;
  --accent-red: #f44336;
  
  --editor-bg: #1e1e1e;
  --editor-line-numbers: #858585;
  --editor-selection: #264f78;
  
  --chat-user-bg: #0f4c75;
  --chat-assistant-bg: #2d2d30;
  
  --sidebar-width: 240px;
  --panel-min-width: 300px;
  --header-height: 35px;
  --statusbar-height: 22px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body,
#root {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  font-family: 'Monaco', 'Cascadia Code', 'Segoe UI Mono', 'Ubuntu Mono', monospace;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 13px;
  line-height: 1.4;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
  background: var(--bg-quaternary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Selection Styling */
::selection {
  background: var(--editor-selection);
}

/* Focus Outline */
*:focus {
  outline: 1px solid var(--accent-blue);
  outline-offset: -1px;
}

/* Button Base Styles */
.btn {
  @apply inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium transition-all duration-150 border-0 rounded cursor-pointer;
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}

.btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-secondary);
}

.btn:active {
  transform: translateY(1px);
}

.btn-primary {
  background: var(--accent-blue);
  color: white;
  border-color: var(--accent-blue);
}

.btn-primary:hover {
  background: var(--accent-blue-hover);
  border-color: var(--accent-blue-hover);
}

.btn-ghost {
  background: transparent;
  border: none;
  color: var(--text-secondary);
}

.btn-ghost:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Input Base Styles */
.input {
  @apply w-full px-3 py-2 text-sm border rounded;
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.input:focus {
  border-color: var(--accent-blue);
  background: var(--bg-primary);
}

.input::placeholder {
  color: var(--text-tertiary);
}

/* Layout Animations */
.panel-resize {
  transition: width 0.2s ease, height 0.2s ease;
}

.fade-in {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

/* Loading Spinner */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--bg-tertiary);
  border-top: 2px solid var(--accent-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Code Highlighting */
.code-block {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 4px;
  padding: 12px;
  font-family: 'Monaco', 'Cascadia Code', monospace;
  font-size: 12px;
  overflow-x: auto;
}

/* Chat Message Styles */
.chat-message {
  @apply mb-4 p-3 rounded-lg;
}

.chat-message.user {
  background: var(--chat-user-bg);
  margin-left: 20px;
}

.chat-message.assistant {
  background: var(--chat-assistant-bg);
  margin-right: 20px;
}

/* File Tree Styles */
.tree-item {
  @apply flex items-center px-2 py-1 cursor-pointer text-sm;
  color: var(--text-primary);
}

.tree-item:hover {
  background: var(--bg-tertiary);
}

.tree-item.selected {
  background: var(--bg-quaternary);
  color: white;
}

.tree-item.modified {
  color: var(--accent-orange);
}

.tree-item.new {
  color: var(--accent-green);
}

/* Tab Styles */
.tab-bar {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  overflow-x: auto;
}

.tab {
  @apply flex items-center px-3 py-2 text-sm cursor-pointer whitespace-nowrap;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border-right: 1px solid var(--border-primary);
  min-width: 120px;
}

.tab:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.tab.active {
  background: var(--bg-primary);
  color: var(--text-primary);
  border-bottom: 2px solid var(--accent-blue);
}

.tab-close {
  @apply ml-2 w-4 h-4 flex items-center justify-center rounded hover:bg-gray-600;
  opacity: 0.6;
}

.tab-close:hover {
  opacity: 1;
}

/* Status Bar */
.status-bar {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  height: var(--statusbar-height);
  @apply flex items-center justify-between px-3 text-xs;
  color: var(--text-secondary);
}

/* Resize Handle */
.resize-handle {
  background: var(--border-primary);
  cursor: col-resize;
  user-select: none;
}

.resize-handle:hover {
  background: var(--accent-blue);
}

.resize-handle.vertical {
  cursor: row-resize;
}

/* Utility Classes */
.text-accent { color: var(--accent-blue); }
.text-success { color: var(--accent-green); }
.text-warning { color: var(--accent-orange); }
.text-error { color: var(--accent-red); }

.bg-accent { background-color: var(--accent-blue); }
.bg-success { background-color: var(--accent-green); }
.bg-warning { background-color: var(--accent-orange); }
.bg-error { background-color: var(--accent-red); }

/* Hide default scrollbars in panels */
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* AI Thinking Animation */
.ai-thinking {
  display: inline-flex;
  gap: 2px;
}

.ai-thinking span {
  width: 4px;
  height: 4px;
  background: var(--accent-blue);
  border-radius: 50%;
  animation: thinking 1.4s ease-in-out infinite both;
}

.ai-thinking span:nth-child(1) { animation-delay: -0.32s; }
.ai-thinking span:nth-child(2) { animation-delay: -0.16s; }
.ai-thinking span:nth-child(3) { animation-delay: 0s; }

@keyframes thinking {
  0%, 80%, 100% { 
    transform: scale(0);
    opacity: 0.5;
  } 
  40% { 
    transform: scale(1);
    opacity: 1;
  }
}

/* Context Menu */
.context-menu {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  padding: 4px 0;
  min-width: 180px;
  z-index: 1000;
}

.context-menu-item {
  @apply flex items-center px-3 py-2 text-sm cursor-pointer;
  color: var(--text-primary);
}

.context-menu-item:hover {
  background: var(--accent-blue);
  color: white;
}

.context-menu-separator {
  height: 1px;
  background: var(--border-primary);
  margin: 4px 0;
}

/* Diff View */
.diff-view {
  font-family: 'Monaco', 'Cascadia Code', monospace;
  font-size: 12px;
}

.diff-line {
  @apply flex;
  line-height: 1.5;
}

.diff-line-number {
  @apply w-12 text-center text-xs;
  color: var(--text-tertiary);
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
  user-select: none;
}

.diff-line-content {
  @apply flex-1 px-2;
}

.diff-line.added {
  background: rgba(76, 175, 80, 0.1);
  border-left: 3px solid var(--accent-green);
}

.diff-line.removed {
  background: rgba(244, 67, 54, 0.1);
  border-left: 3px solid var(--accent-red);
}

/* Tooltip */
.tooltip {
  background: var(--bg-quaternary);
  color: var(--text-primary);
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 11px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 1001;
} 