[tool:pytest]
# 测试发现配置
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --strict-markers
    --strict-config
    --tb=short
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80

# 异步测试配置
asyncio_mode = auto

# 标记配置
markers =
    unit: 单元测试
    integration: 集成测试
    e2e: 端到端测试
    slow: 慢速测试
    api: API测试
    vfs: 虚拟文件系统测试
    context: 上下文引擎测试
    diff: Diff引擎测试
    ai: AI相关测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# 最小版本要求
minversion = 7.0 