import React, { useState, useEffect, useRef } from 'react';
import {
  Search,
  FileText,
  Folder,
  Settings,
  Terminal,
  Code,
  MessageSquare,
  Zap,
  GitBranch,
  Play,
  RefreshCw,
  X,
  ChevronRight
} from 'lucide-react';

interface Command {
  id: string;
  title: string;
  description?: string;
  category: string;
  icon: React.ElementType;
  shortcut?: string;
  action: () => void;
}

interface CommandPaletteProps {
  onClose: () => void;
  onCommand: (command: string) => void;
}

const CommandPalette: React.FC<CommandPaletteProps> = ({ onClose, onCommand }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  // Mock commands - in a real app, these would come from various sources
  const allCommands: Command[] = [
    // File operations
    {
      id: 'file.new',
      title: 'New File',
      description: 'Create a new file',
      category: 'File',
      icon: FileText,
      shortcut: '⌘N',
      action: () => onCommand('new-file')
    },
    {
      id: 'file.open',
      title: 'Open File',
      description: 'Open a file from the workspace',
      category: 'File',
      icon: Folder,
      shortcut: '⌘O',
      action: () => onCommand('open-file')
    },
    {
      id: 'file.save',
      title: 'Save File',
      description: 'Save the current file',
      category: 'File',
      icon: FileText,
      shortcut: '⌘S',
      action: () => onCommand('save-file')
    },
    
    // View operations
    {
      id: 'view.toggle-sidebar',
      title: 'Toggle Sidebar',
      description: 'Show or hide the sidebar',
      category: 'View',
      icon: Folder,
      shortcut: '⌘B',
      action: () => onCommand('toggle-sidebar')
    },
    {
      id: 'view.toggle-terminal',
      title: 'Toggle Terminal',
      description: 'Show or hide the terminal',
      category: 'View',
      icon: Terminal,
      shortcut: '⌃`',
      action: () => onCommand('toggle-terminal')
    },
    {
      id: 'view.toggle-chat',
      title: 'Toggle AI Chat',
      description: 'Show or hide the AI chat panel',
      category: 'View',
      icon: MessageSquare,
      shortcut: '⌘⇧C',
      action: () => onCommand('toggle-chat')
    },
    
    // AI operations
    {
      id: 'ai.explain-code',
      title: 'Explain Code',
      description: 'Get AI explanation of selected code',
      category: 'AI',
      icon: Zap,
      action: () => onCommand('ai-explain')
    },
    {
      id: 'ai.optimize-code',
      title: 'Optimize Code',
      description: 'Get AI suggestions to optimize code',
      category: 'AI',
      icon: Zap,
      action: () => onCommand('ai-optimize')
    },
    {
      id: 'ai.generate-tests',
      title: 'Generate Tests',
      description: 'Generate unit tests for current code',
      category: 'AI',
      icon: Code,
      action: () => onCommand('ai-tests')
    },
    
    // Git operations
    {
      id: 'git.status',
      title: 'Git Status',
      description: 'Show git status',
      category: 'Git',
      icon: GitBranch,
      action: () => onCommand('git-status')
    },
    {
      id: 'git.commit',
      title: 'Git Commit',
      description: 'Commit changes',
      category: 'Git',
      icon: GitBranch,
      action: () => onCommand('git-commit')
    },
    
    // Settings
    {
      id: 'settings.preferences',
      title: 'Preferences',
      description: 'Open settings',
      category: 'Settings',
      icon: Settings,
      shortcut: '⌘,',
      action: () => onCommand('open-settings')
    },
    {
      id: 'settings.keybindings',
      title: 'Keyboard Shortcuts',
      description: 'View and edit keyboard shortcuts',
      category: 'Settings',
      icon: Settings,
      action: () => onCommand('open-keybindings')
    },
    
    // Developer
    {
      id: 'dev.reload',
      title: 'Reload Window',
      description: 'Reload the application',
      category: 'Developer',
      icon: RefreshCw,
      shortcut: '⌘R',
      action: () => window.location.reload()
    }
  ];

  // Filter commands based on search term
  const filteredCommands = allCommands.filter(command =>
    command.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    command.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    command.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Group commands by category
  const groupedCommands = filteredCommands.reduce((groups, command) => {
    const category = command.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(command);
    return groups;
  }, {} as { [key: string]: Command[] });

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => Math.min(prev + 1, filteredCommands.length - 1));
          break;
          
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => Math.max(prev - 1, 0));
          break;
          
        case 'Enter':
          e.preventDefault();
          if (filteredCommands[selectedIndex]) {
            filteredCommands[selectedIndex].action();
            onClose();
          }
          break;
          
        case 'Escape':
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [filteredCommands, selectedIndex, onClose]);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Reset selected index when search changes
  useEffect(() => {
    setSelectedIndex(0);
  }, [searchTerm]);

  // Scroll selected item into view
  useEffect(() => {
    const selectedElement = listRef.current?.children[selectedIndex] as HTMLElement;
    if (selectedElement) {
      selectedElement.scrollIntoView({ block: 'nearest' });
    }
  }, [selectedIndex]);

  const executeCommand = (command: Command) => {
    command.action();
    onClose();
  };

  let commandIndex = 0;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-20 z-50">
      <div className="bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg shadow-2xl w-full max-w-2xl max-h-96 flex flex-col">
        {/* Search Input */}
        <div className="p-4 border-b border-[var(--border-primary)]">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[var(--text-tertiary)]" />
            <input
              ref={inputRef}
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Type a command or search..."
              className="w-full pl-10 pr-10 py-2 bg-transparent border-none text-[var(--text-primary)] placeholder-[var(--text-tertiary)] focus:outline-none"
            />
            <button
              onClick={onClose}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[var(--text-tertiary)] hover:text-[var(--text-primary)]"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Commands List */}
        <div ref={listRef} className="flex-1 overflow-auto">
          {filteredCommands.length === 0 ? (
            <div className="p-8 text-center text-[var(--text-tertiary)]">
              <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No commands found</p>
            </div>
          ) : (
            Object.entries(groupedCommands).map(([category, commands]) => (
              <div key={category}>
                <div className="px-4 py-2 text-xs font-medium text-[var(--text-secondary)] uppercase tracking-wide bg-[var(--bg-tertiary)]">
                  {category}
                </div>
                {commands.map((command) => {
                  const isSelected = commandIndex === selectedIndex;
                  const currentIndex = commandIndex++;
                  
                  return (
                    <div
                      key={command.id}
                      onClick={() => executeCommand(command)}
                      className={`px-4 py-3 cursor-pointer flex items-center justify-between hover:bg-[var(--bg-tertiary)] ${
                        isSelected ? 'bg-[var(--bg-quaternary)]' : ''
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <command.icon className="w-4 h-4 text-[var(--text-secondary)]" />
                        <div>
                          <div className="text-sm font-medium text-[var(--text-primary)]">
                            {command.title}
                          </div>
                          {command.description && (
                            <div className="text-xs text-[var(--text-tertiary)]">
                              {command.description}
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {command.shortcut && (
                          <span className="text-xs text-[var(--text-tertiary)] bg-[var(--bg-quaternary)] px-2 py-1 rounded">
                            {command.shortcut}
                          </span>
                        )}
                        {isSelected && (
                          <ChevronRight className="w-4 h-4 text-[var(--text-secondary)]" />
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            ))
          )}
        </div>

        {/* Footer */}
        <div className="px-4 py-2 border-t border-[var(--border-primary)] text-xs text-[var(--text-tertiary)] flex items-center justify-between">
          <span>Use ↑↓ to navigate, Enter to select, Esc to close</span>
          <span>{filteredCommands.length} commands</span>
        </div>
      </div>
    </div>
  );
};

export default CommandPalette; 