"""
虚拟文件系统 (Virtual File System)

提供内存中的高性能文件管理系统，支持文件树管理、内容存储、
版本控制和批量操作，为AI编程工具提供核心的文件管理能力。

主要组件：
- FileNode: 文件系统节点（文件/目录）
- VirtualFileSystem: 虚拟文件系统管理器
- FileFilter: 文件过滤器
- FileSearcher: 文件搜索引擎
"""

import os
import mimetypes
import threading
from datetime import datetime
from pathlib import Path
from typing import (
    Dict, List, Optional, Union, Any, Callable, Iterator, 
    Set, Tuple, TypeVar, Generic
)
from enum import Enum
import json
import pickle
import hashlib
import re
from dataclasses import dataclass, field


class FileType(Enum):
    """文件类型枚举"""
    FILE = "file"
    DIRECTORY = "directory"
    SYMLINK = "symlink"


class FilePermission(Enum):
    """文件权限枚举"""
    READ = "r"
    WRITE = "w"
    EXECUTE = "x"


@dataclass
class FileMetadata:
    """文件元数据"""
    size: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    modified_at: datetime = field(default_factory=datetime.now)
    accessed_at: datetime = field(default_factory=datetime.now)
    mime_type: Optional[str] = None
    encoding: str = "utf-8"
    language: Optional[str] = None
    permissions: Set[FilePermission] = field(default_factory=lambda: {
        FilePermission.READ, FilePermission.WRITE
    })
    custom_attributes: Dict[str, Any] = field(default_factory=dict)


@dataclass
class FileVersion:
    """文件版本信息"""
    version_id: str
    content: Union[str, bytes]
    timestamp: datetime
    comment: str = ""
    checksum: str = ""


class FileNode:
    """
    文件系统节点类
    
    表示文件系统中的一个节点，可以是文件或目录。
    支持内容存储、版本控制、元数据管理等功能。
    """
    
    def __init__(
        self, 
        name: str, 
        file_type: FileType = FileType.FILE,
        content: Union[str, bytes, None] = None,
        parent: Optional['FileNode'] = None
    ):
        """
        初始化文件节点
        
        Args:
            name: 文件/目录名称
            file_type: 文件类型
            content: 文件内容（仅对文件类型有效）
            parent: 父节点
        """
        self.name: str = name
        self.file_type: FileType = file_type
        self.parent: Optional[FileNode] = parent
        self.metadata: FileMetadata = FileMetadata()
        
        # 内容存储
        self._content: Union[str, bytes, None] = None
        self._versions: List[FileVersion] = []
        
        # 子节点（仅目录类型使用）
        self._children: Dict[str, FileNode] = {}
        
        # 线程安全
        self._lock = threading.RLock()
        
        # 初始化内容
        if content is not None:
            self.set_content(content)
    
    @property
    def path(self) -> str:
        """获取节点的完整路径"""
        if self.parent is None:
            return "/" if self.name == "/" else f"/{self.name}"
        
        parent_path = self.parent.path
        if parent_path == "/":
            return f"/{self.name}"
        else:
            return f"{parent_path}/{self.name}"
    
    @property
    def content(self) -> Union[str, bytes, None]:
        """获取文件内容"""
        with self._lock:
            return self._content
    
    @property
    def children(self) -> Dict[str, 'FileNode']:
        """获取子节点（只读）"""
        with self._lock:
            return dict(self._children)  # 返回副本保证只读
    
    @property
    def is_file(self) -> bool:
        """判断是否为文件"""
        return self.file_type == FileType.FILE
    
    @property
    def is_directory(self) -> bool:
        """判断是否为目录"""
        return self.file_type == FileType.DIRECTORY
    
    def set_content(self, content: Union[str, bytes]) -> None:
        """
        设置文件内容
        
        Args:
            content: 文件内容
            
        Raises:
            ValueError: 如果节点不是文件类型
        """
        if not self.is_file:
            raise ValueError(f"Cannot set content for {self.file_type.value}")
        
        with self._lock:
            self._content = content
            self.metadata.modified_at = datetime.now()
            
            # 更新文件大小
            if isinstance(content, str):
                self.metadata.size = len(content.encode(self.metadata.encoding))
            else:
                self.metadata.size = len(content)
            
            # 自动检测MIME类型
            self._detect_mime_type()
    
    def get_content(self, encoding: Optional[str] = None) -> Union[str, bytes]:
        """
        获取文件内容
        
        Args:
            encoding: 文本编码（如果指定则返回字符串）
            
        Returns:
            文件内容
        """
        if not self.is_file:
            raise ValueError(f"Cannot get content from {self.file_type.value}")
        
        with self._lock:
            self.metadata.accessed_at = datetime.now()
            
            if self._content is None:
                return b"" if encoding is None else ""
            
            if encoding is not None:
                if isinstance(self._content, bytes):
                    return self._content.decode(encoding)
                else:
                    return self._content
            else:
                if isinstance(self._content, str):
                    return self._content.encode(self.metadata.encoding)
                else:
                    return self._content
    
    def add_child(self, child: 'FileNode') -> None:
        """
        添加子节点
        
        Args:
            child: 子节点
            
        Raises:
            ValueError: 如果当前节点不是目录类型
            FileExistsError: 如果子节点已存在
        """
        if not self.is_directory:
            raise ValueError(f"Cannot add child to {self.file_type.value}")
        
        with self._lock:
            if child.name in self._children:
                raise FileExistsError(f"Child '{child.name}' already exists")
            
            self._children[child.name] = child
            child.parent = self
            self.metadata.modified_at = datetime.now()
    
    def remove_child(self, name: str) -> Optional['FileNode']:
        """
        移除子节点
        
        Args:
            name: 子节点名称
            
        Returns:
            被移除的节点，如果不存在则返回None
        """
        if not self.is_directory:
            return None
        
        with self._lock:
            child = self._children.pop(name, None)
            if child:
                child.parent = None
                self.metadata.modified_at = datetime.now()
            return child
    
    def get_child(self, name: str) -> Optional['FileNode']:
        """
        获取子节点
        
        Args:
            name: 子节点名称
            
        Returns:
            子节点，如果不存在则返回None
        """
        with self._lock:
            return self._children.get(name)
    
    def list_children(self, include_hidden: bool = False) -> List['FileNode']:
        """
        列出所有子节点
        
        Args:
            include_hidden: 是否包含隐藏文件
            
        Returns:
            子节点列表
        """
        with self._lock:
            children = list(self._children.values())
            
            if not include_hidden:
                children = [child for child in children if not child.name.startswith('.')]
            
            # 按名称排序，目录在前
            children.sort(key=lambda x: (x.is_file, x.name.lower()))
            return children
    
    def create_version(self, comment: str = "") -> str:
        """
        创建当前内容的版本快照
        
        Args:
            comment: 版本注释
            
        Returns:
            版本ID
        """
        if not self.is_file or self._content is None:
            raise ValueError("Cannot create version for directory or empty file")
        
        with self._lock:
            # 生成版本ID
            version_id = hashlib.md5(
                f"{self.path}_{datetime.now().isoformat()}".encode()
            ).hexdigest()[:12]
            
            # 计算内容校验和
            if isinstance(self._content, str):
                content_bytes = self._content.encode(self.metadata.encoding)
            else:
                content_bytes = self._content
            
            checksum = hashlib.sha256(content_bytes).hexdigest()
            
            # 创建版本记录
            version = FileVersion(
                version_id=version_id,
                content=self._content,
                timestamp=datetime.now(),
                comment=comment,
                checksum=checksum
            )
            
            self._versions.append(version)
            return version_id
    
    def restore_version(self, version_id: str) -> bool:
        """
        恢复到指定版本
        
        Args:
            version_id: 版本ID
            
        Returns:
            是否成功恢复
        """
        with self._lock:
            for version in self._versions:
                if version.version_id == version_id:
                    self._content = version.content
                    self.metadata.modified_at = datetime.now()
                    return True
            return False
    
    def get_versions(self) -> List[FileVersion]:
        """获取所有版本列表"""
        with self._lock:
            return list(self._versions)  # 返回副本
    
    def update_metadata(self, **kwargs) -> None:
        """
        更新文件元数据
        
        Args:
            **kwargs: 元数据字段
        """
        with self._lock:
            for key, value in kwargs.items():
                if hasattr(self.metadata, key):
                    setattr(self.metadata, key, value)
                else:
                    self.metadata.custom_attributes[key] = value
            
            self.metadata.modified_at = datetime.now()
    
    def calculate_size(self) -> int:
        """
        计算节点大小
        
        对于文件，返回内容大小
        对于目录，返回所有子节点的总大小
        
        Returns:
            节点大小（字节）
        """
        with self._lock:
            if self.is_file:
                return self.metadata.size
            else:
                total_size = 0
                for child in self._children.values():
                    total_size += child.calculate_size()
                return total_size
    
    def _detect_mime_type(self) -> None:
        """自动检测文件MIME类型"""
        if self.is_file:
            mime_type, _ = mimetypes.guess_type(self.name)
            self.metadata.mime_type = mime_type
            
            # 检测编程语言
            ext = Path(self.name).suffix.lower()
            language_map = {
                '.py': 'python', '.js': 'javascript', '.ts': 'typescript',
                '.jsx': 'javascript', '.tsx': 'typescript', '.html': 'html',
                '.css': 'css', '.scss': 'scss', '.less': 'less',
                '.json': 'json', '.xml': 'xml', '.yaml': 'yaml', '.yml': 'yaml',
                '.md': 'markdown', '.txt': 'text', '.sql': 'sql',
                '.sh': 'bash', '.bat': 'batch', '.ps1': 'powershell',
                '.php': 'php', '.rb': 'ruby', '.go': 'go', '.rs': 'rust',
                '.java': 'java', '.c': 'c', '.cpp': 'cpp', '.h': 'c',
                '.hpp': 'cpp', '.cs': 'csharp', '.swift': 'swift', '.kt': 'kotlin'
            }
            self.metadata.language = language_map.get(ext)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将节点序列化为字典
        
        Returns:
            序列化后的字典
        """
        with self._lock:
            data = {
                'name': self.name,
                'file_type': self.file_type.value,
                'metadata': {
                    'size': self.metadata.size,
                    'created_at': self.metadata.created_at.isoformat(),
                    'modified_at': self.metadata.modified_at.isoformat(),
                    'accessed_at': self.metadata.accessed_at.isoformat(),
                    'mime_type': self.metadata.mime_type,
                    'encoding': self.metadata.encoding,
                    'language': self.metadata.language,
                    'permissions': [p.value for p in self.metadata.permissions],
                    'custom_attributes': self.metadata.custom_attributes
                },
                'versions': [
                    {
                        'version_id': v.version_id,
                        'timestamp': v.timestamp.isoformat(),
                        'comment': v.comment,
                        'checksum': v.checksum,
                        'content': v.content if isinstance(v.content, str) else v.content.decode(self.metadata.encoding, errors='ignore')
                    }
                    for v in self._versions
                ]
            }
            
            if self.is_file and self._content is not None:
                if isinstance(self._content, str):
                    data['content'] = self._content
                else:
                    data['content'] = self._content.decode(self.metadata.encoding, errors='ignore')
            
            if self.is_directory:
                data['children'] = {
                    name: child.to_dict() for name, child in self._children.items()
                }
            
            return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FileNode':
        """
        从字典反序列化节点
        
        Args:
            data: 序列化的数据
            
        Returns:
            文件节点实例
        """
        file_type = FileType(data['file_type'])
        content = data.get('content')
        
        # 如果是字节内容，进行编码
        if content and data['metadata'].get('encoding'):
            encoding = data['metadata']['encoding']
            if isinstance(content, str) and file_type == FileType.FILE:
                try:
                    content = content.encode(encoding)
                except UnicodeEncodeError:
                    pass  # 保持字符串格式
        
        node = cls(
            name=data['name'],
            file_type=file_type,
            content=content
        )
        
        # 恢复元数据
        metadata = data['metadata']
        node.metadata.size = metadata['size']
        node.metadata.created_at = datetime.fromisoformat(metadata['created_at'])
        node.metadata.modified_at = datetime.fromisoformat(metadata['modified_at'])
        node.metadata.accessed_at = datetime.fromisoformat(metadata['accessed_at'])
        node.metadata.mime_type = metadata.get('mime_type')
        node.metadata.encoding = metadata.get('encoding', 'utf-8')
        node.metadata.language = metadata.get('language')
        node.metadata.permissions = {
            FilePermission(p) for p in metadata.get('permissions', ['r', 'w'])
        }
        node.metadata.custom_attributes = metadata.get('custom_attributes', {})
        
        # 恢复版本历史
        for version_data in data.get('versions', []):
            version = FileVersion(
                version_id=version_data['version_id'],
                content=version_data['content'],
                timestamp=datetime.fromisoformat(version_data['timestamp']),
                comment=version_data['comment'],
                checksum=version_data['checksum']
            )
            node._versions.append(version)
        
        # 递归恢复子节点
        if file_type == FileType.DIRECTORY and 'children' in data:
            for child_data in data['children'].values():
                child = cls.from_dict(child_data)
                node.add_child(child)
        
        return node


class VirtualFileSystem:
    """
    虚拟文件系统管理器
    
    提供类似Unix文件系统的操作接口，支持路径操作、
    文件管理、搜索过滤、批量操作等功能。
    """
    
    def __init__(self, root_name: str = "/"):
        """
        初始化虚拟文件系统
        
        Args:
            root_name: 根目录名称
        """
        self._root: FileNode = FileNode(root_name, FileType.DIRECTORY)
        self._cwd: FileNode = self._root  # 当前工作目录
        self._lock = threading.RLock()
        
        # 操作历史
        self._operation_history: List[Dict[str, Any]] = []
        
        # 文件索引（用于快速搜索）
        self._file_index: Dict[str, Set[FileNode]] = {}
        self._content_index: Dict[str, Set[FileNode]] = {}
    
    @property
    def root(self) -> FileNode:
        """获取根节点"""
        return self._root
    
    @property
    def cwd(self) -> FileNode:
        """获取当前工作目录"""
        return self._cwd
    
    @property
    def current_path(self) -> str:
        """获取当前工作目录路径"""
        return self._cwd.path
    
    def resolve_path(self, path: str) -> Optional[FileNode]:
        """
        解析路径，返回对应的节点
        
        Args:
            path: 文件路径（支持绝对路径和相对路径）
            
        Returns:
            对应的文件节点，如果路径不存在则返回None
        """
        with self._lock:
            path = self.normalize_path(path)
            
            # 处理根路径
            if path == "/":
                return self._root
            
            # 分割路径
            parts = [p for p in path.split("/") if p]
            current = self._root
            
            # 逐级查找
            for part in parts:
                if not current.is_directory:
                    return None
                current = current.get_child(part)
                if current is None:
                    return None
            
            return current
    
    def normalize_path(self, path: str) -> str:
        """
        规范化路径
        
        Args:
            path: 原始路径
            
        Returns:
            规范化后的路径
        """
        if not path:
            return self.current_path
        
        # 处理相对路径
        if not path.startswith("/"):
            if self.current_path == "/":
                path = "/" + path
            else:
                path = self.current_path + "/" + path
        
        # 使用Path进行规范化
        normalized = str(Path(path)).replace("\\", "/")
        
        # 确保以/开头
        if not normalized.startswith("/"):
            normalized = "/" + normalized
        
        return normalized
    
    def exists(self, path: str) -> bool:
        """
        检查路径是否存在
        
        Args:
            path: 文件路径
            
        Returns:
            路径是否存在
        """
        return self.resolve_path(path) is not None
    
    def is_file(self, path: str) -> bool:
        """
        检查路径是否为文件
        
        Args:
            path: 文件路径
            
        Returns:
            是否为文件
        """
        node = self.resolve_path(path)
        return node is not None and node.is_file
    
    def is_directory(self, path: str) -> bool:
        """
        检查路径是否为目录
        
        Args:
            path: 文件路径
            
        Returns:
            是否为目录
        """
        node = self.resolve_path(path)
        return node is not None and node.is_directory
    
    def mkdir(self, path: str, parents: bool = False) -> bool:
        """
        创建目录
        
        Args:
            path: 目录路径
            parents: 是否创建父目录
            
        Returns:
            是否创建成功
        """
        with self._lock:
            path = self.normalize_path(path)
            
            # 检查是否已存在
            if self.exists(path):
                return False
            
            # 分割路径
            parent_path = str(Path(path).parent)
            dir_name = Path(path).name
            
            # 处理根目录的特殊情况
            if parent_path == path:  # 根目录
                return False
            
            # 获取父目录
            parent_node = self.resolve_path(parent_path)
            
            if parent_node is None:
                if parents:
                    # 递归创建父目录
                    if not self.mkdir(parent_path, parents=True):
                        return False
                    parent_node = self.resolve_path(parent_path)
                else:
                    return False
            
            if not parent_node.is_directory:
                return False
            
            # 创建目录节点
            try:
                new_dir = FileNode(dir_name, FileType.DIRECTORY)
                parent_node.add_child(new_dir)
                self._log_operation("mkdir", path)
                return True
            except FileExistsError:
                return False
    
    def touch(self, path: str, content: Union[str, bytes] = "") -> bool:
        """
        创建文件
        
        Args:
            path: 文件路径
            content: 初始内容
            
        Returns:
            是否创建成功
        """
        with self._lock:
            path = self.normalize_path(path)
            
            # 检查是否已存在
            if self.exists(path):
                return False
            
            # 分割路径
            parent_path = str(Path(path).parent)
            file_name = Path(path).name
            
            # 获取父目录
            parent_node = self.resolve_path(parent_path)
            if parent_node is None or not parent_node.is_directory:
                return False
            
            # 创建文件节点
            try:
                new_file = FileNode(file_name, FileType.FILE, content)
                parent_node.add_child(new_file)
                self._log_operation("touch", path)
                return True
            except FileExistsError:
                return False
    
    def remove(self, path: str, recursive: bool = False) -> bool:
        """
        删除文件或目录
        
        Args:
            path: 文件路径
            recursive: 是否递归删除
            
        Returns:
            是否删除成功
        """
        with self._lock:
            path = self.normalize_path(path)
            
            # 不能删除根目录
            if path == "/":
                return False
            
            node = self.resolve_path(path)
            if node is None:
                return False
            
            # 检查目录是否为空
            if node.is_directory and len(node.children) > 0 and not recursive:
                return False
            
            # 获取父目录并移除节点
            if node.parent:
                node.parent.remove_child(node.name)
                self._log_operation("remove", path)
                return True
            
            return False
    
    def move(self, src_path: str, dst_path: str) -> bool:
        """
        移动/重命名文件或目录
        
        Args:
            src_path: 源路径
            dst_path: 目标路径
            
        Returns:
            是否移动成功
        """
        with self._lock:
            src_path = self.normalize_path(src_path)
            dst_path = self.normalize_path(dst_path)
            
            # 不能移动根目录
            if src_path == "/":
                return False
            
            src_node = self.resolve_path(src_path)
            if src_node is None:
                return False
            
            # 检查目标路径是否已存在
            if self.exists(dst_path):
                return False
            
            # 分割目标路径
            dst_parent_path = str(Path(dst_path).parent)
            dst_name = Path(dst_path).name
            
            # 获取目标父目录
            dst_parent = self.resolve_path(dst_parent_path)
            if dst_parent is None or not dst_parent.is_directory:
                return False
            
            # 执行移动
            if src_node.parent:
                src_node.parent.remove_child(src_node.name)
            
            src_node.name = dst_name
            dst_parent.add_child(src_node)
            
            self._log_operation("move", f"{src_path} -> {dst_path}")
            return True
    
    def copy(self, src_path: str, dst_path: str, deep: bool = True) -> bool:
        """
        复制文件或目录
        
        Args:
            src_path: 源路径
            dst_path: 目标路径
            deep: 是否深度复制
            
        Returns:
            是否复制成功
        """
        with self._lock:
            src_path = self.normalize_path(src_path)
            dst_path = self.normalize_path(dst_path)
            
            src_node = self.resolve_path(src_path)
            if src_node is None:
                return False
            
            # 检查目标路径是否已存在
            if self.exists(dst_path):
                return False
            
            # 分割目标路径
            dst_parent_path = str(Path(dst_path).parent)
            dst_name = Path(dst_path).name
            
            # 获取目标父目录
            dst_parent = self.resolve_path(dst_parent_path)
            if dst_parent is None or not dst_parent.is_directory:
                return False
            
            # 执行复制
            try:
                copied_node = self._copy_node(src_node, dst_name, deep)
                dst_parent.add_child(copied_node)
                self._log_operation("copy", f"{src_path} -> {dst_path}")
                return True
            except Exception:
                return False
    
    def read_file(self, path: str, encoding: Optional[str] = None) -> Union[str, bytes]:
        """
        读取文件内容
        
        Args:
            path: 文件路径
            encoding: 文本编码
            
        Returns:
            文件内容
            
        Raises:
            FileNotFoundError: 文件不存在
            IsADirectoryError: 路径是目录
        """
        node = self.resolve_path(path)
        if node is None:
            raise FileNotFoundError(f"File not found: {path}")
        
        if node.is_directory:
            raise IsADirectoryError(f"Is a directory: {path}")
        
        return node.get_content(encoding)
    
    def write_file(self, path: str, content: Union[str, bytes], encoding: str = "utf-8") -> bool:
        """
        写入文件内容
        
        Args:
            path: 文件路径
            content: 文件内容
            encoding: 文本编码
            
        Returns:
            是否写入成功
        """
        with self._lock:
            node = self.resolve_path(path)
            if node is None:
                # 文件不存在，创建新文件
                return self.touch(path, content)
            
            if node.is_directory:
                return False
            
            try:
                # 处理编码
                if isinstance(content, str) and encoding != node.metadata.encoding:
                    node.metadata.encoding = encoding
                
                node.set_content(content)
                self._log_operation("write", path)
                return True
            except Exception:
                return False
    
    def append_file(self, path: str, content: Union[str, bytes], encoding: str = "utf-8") -> bool:
        """
        追加文件内容
        
        Args:
            path: 文件路径
            content: 追加内容
            encoding: 文本编码
            
        Returns:
            是否追加成功
        """
        with self._lock:
            node = self.resolve_path(path)
            if node is None:
                # 文件不存在，创建新文件
                return self.touch(path, content)
            
            if node.is_directory:
                return False
            
            try:
                # 获取现有内容
                existing_content = node.content or ""
                
                # 合并内容
                if isinstance(existing_content, str) and isinstance(content, str):
                    new_content = existing_content + content
                elif isinstance(existing_content, bytes) and isinstance(content, bytes):
                    new_content = existing_content + content
                else:
                    # 类型不匹配，转换为字符串
                    if isinstance(existing_content, bytes):
                        existing_content = existing_content.decode(encoding)
                    if isinstance(content, bytes):
                        content = content.decode(encoding)
                    new_content = existing_content + content
                
                node.set_content(new_content)
                self._log_operation("append", path)
                return True
            except Exception:
                return False
    
    def list_directory(self, path: str = ".", include_hidden: bool = False) -> List[str]:
        """
        列出目录内容
        
        Args:
            path: 目录路径
            include_hidden: 是否包含隐藏文件
            
        Returns:
            文件和目录名称列表
        """
        node = self.resolve_path(path)
        if node is None or not node.is_directory:
            return []
        
        children = node.list_children(include_hidden)
        return [child.name for child in children]
    
    def walk(self, path: str = ".") -> Iterator[Tuple[str, List[str], List[str]]]:
        """
        递归遍历目录树
        
        Args:
            path: 起始路径
            
        Yields:
            (当前目录路径, 子目录列表, 文件列表)
        """
        node = self.resolve_path(path)
        if node is None or not node.is_directory:
            return
        
        # 获取子节点
        children = node.list_children(include_hidden=True)
        dirs = [child.name for child in children if child.is_directory]
        files = [child.name for child in children if child.is_file]
        
        # 返回当前目录信息
        yield (self.normalize_path(path), dirs, files)
        
        # 递归遍历子目录
        for dir_name in dirs:
            child_path = self.normalize_path(f"{path}/{dir_name}")
            yield from self.walk(child_path)
    
    def find(self, pattern: str, path: str = ".", file_type: Optional[FileType] = None) -> List[str]:
        """
        查找文件
        
        Args:
            pattern: 搜索模式（支持通配符）
            path: 搜索路径
            file_type: 文件类型过滤
            
        Returns:
            匹配的文件路径列表
        """
        import fnmatch
        
        results = []
        
        # 遍历目录树
        for current_path, dirs, files in self.walk(path):
            # 检查文件
            if file_type is None or file_type == FileType.FILE:
                for file_name in files:
                    if fnmatch.fnmatch(file_name, pattern):
                        file_path = f"{current_path}/{file_name}" if current_path != "/" else f"/{file_name}"
                        results.append(file_path)
            
            # 检查目录
            if file_type is None or file_type == FileType.DIRECTORY:
                for dir_name in dirs:
                    if fnmatch.fnmatch(dir_name, pattern):
                        dir_path = f"{current_path}/{dir_name}" if current_path != "/" else f"/{dir_name}"
                        results.append(dir_path)
        
        return results
    
    def import_from_filesystem(self, real_path: str, virtual_path: str = ".", 
                              filter_func: Optional[Callable[[str], bool]] = None) -> bool:
        """
        从真实文件系统导入文件和目录
        
        Args:
            real_path: 真实文件系统路径
            virtual_path: 虚拟文件系统目标路径
            filter_func: 文件过滤函数
            
        Returns:
            是否导入成功
        """
        try:
            real_path_obj = Path(real_path)
            if not real_path_obj.exists():
                return False
            
            # 确保目标目录存在
            if not self.exists(virtual_path):
                if not self.mkdir(virtual_path, parents=True):
                    return False
            
            # 递归导入
            return self._import_node(real_path_obj, virtual_path, filter_func)
        
        except Exception:
            return False
    
    def export_to_filesystem(self, virtual_path: str, real_path: str) -> bool:
        """
        导出到真实文件系统
        
        Args:
            virtual_path: 虚拟文件系统路径
            real_path: 真实文件系统目标路径
            
        Returns:
            是否导出成功
        """
        try:
            node = self.resolve_path(virtual_path)
            if node is None:
                return False
            
            real_path_obj = Path(real_path)
            
            if node.is_file:
                # 导出文件
                real_path_obj.parent.mkdir(parents=True, exist_ok=True)
                content = node.content
                if isinstance(content, str):
                    real_path_obj.write_text(content, encoding=node.metadata.encoding)
                else:
                    real_path_obj.write_bytes(content or b"")
            else:
                # 导出目录
                real_path_obj.mkdir(parents=True, exist_ok=True)
                for child in node.children.values():
                    child_real_path = real_path_obj / child.name
                    self.export_to_filesystem(child.path, str(child_real_path))
            
            return True
        
        except Exception:
            return False
    
    def save_state(self, filepath: str, format: str = "json") -> bool:
        """
        保存文件系统状态
        
        Args:
            filepath: 保存文件路径
            format: 保存格式（json/pickle）
            
        Returns:
            是否保存成功
        """
        try:
            with self._lock:
                state_data = {
                    "root": self._root.to_dict(),
                    "current_path": self.current_path,
                    "operation_history": self._operation_history[-100:],  # 只保存最近100条操作
                    "metadata": {
                        "saved_at": datetime.now().isoformat(),
                        "format_version": "1.0"
                    }
                }
                
                filepath_obj = Path(filepath)
                filepath_obj.parent.mkdir(parents=True, exist_ok=True)
                
                if format.lower() == "json":
                    with open(filepath, 'w', encoding='utf-8') as f:
                        json.dump(state_data, f, indent=2, ensure_ascii=False)
                elif format.lower() == "pickle":
                    with open(filepath, 'wb') as f:
                        pickle.dump(state_data, f)
                else:
                    return False
                
                return True
        
        except Exception:
            return False
    
    def load_state(self, filepath: str, format: str = "json") -> bool:
        """
        加载文件系统状态
        
        Args:
            filepath: 状态文件路径
            format: 文件格式（json/pickle）
            
        Returns:
            是否加载成功
        """
        try:
            filepath_obj = Path(filepath)
            if not filepath_obj.exists():
                return False
            
            with self._lock:
                if format.lower() == "json":
                    with open(filepath, 'r', encoding='utf-8') as f:
                        state_data = json.load(f)
                elif format.lower() == "pickle":
                    with open(filepath, 'rb') as f:
                        state_data = pickle.load(f)
                else:
                    return False
                
                # 恢复文件系统状态
                self._root = FileNode.from_dict(state_data["root"])
                
                # 恢复当前工作目录
                current_path = state_data.get("current_path", "/")
                self._cwd = self.resolve_path(current_path) or self._root
                
                # 恢复操作历史
                self._operation_history = state_data.get("operation_history", [])
                
                return True
        
        except Exception:
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取文件系统统计信息
        
        Returns:
            统计信息字典
        """
        with self._lock:
            stats = {
                "total_files": 0,
                "total_directories": 0,
                "total_size": 0,
                "max_depth": 0,
                "file_types": {},
                "operation_count": len(self._operation_history)
            }
            
            # 递归统计
            self._collect_stats(self._root, stats, 0)
            
            return stats
    
    def _import_node(self, real_path: Path, virtual_parent: str, filter_func: Optional[Callable[[str], bool]]) -> bool:
        """递归导入节点"""
        try:
            if filter_func and not filter_func(str(real_path)):
                return True
            
            if real_path.is_file():
                # 导入文件
                try:
                    # 尝试读取为文本
                    content = real_path.read_text(encoding='utf-8')
                except UnicodeDecodeError:
                    # 读取为二进制
                    content = real_path.read_bytes()
                
                virtual_file_path = f"{virtual_parent}/{real_path.name}" if virtual_parent != "/" else f"/{real_path.name}"
                return self.touch(virtual_file_path, content)
            
            elif real_path.is_dir():
                # 导入目录
                virtual_dir_path = f"{virtual_parent}/{real_path.name}" if virtual_parent != "/" else f"/{real_path.name}"
                
                if not self.mkdir(virtual_dir_path):
                    return False
                
                # 递归导入子项
                for child in real_path.iterdir():
                    if not self._import_node(child, virtual_dir_path, filter_func):
                        return False
                
                return True
            
            return True
        
        except Exception:
            return False
    
    def _copy_node(self, src_node: FileNode, new_name: str, deep: bool = True) -> FileNode:
        """复制节点"""
        if src_node.is_file:
            # 复制文件
            new_node = FileNode(new_name, FileType.FILE, src_node.content)
            # 复制元数据
            new_node.metadata = FileMetadata(
                size=src_node.metadata.size,
                created_at=datetime.now(),
                modified_at=src_node.metadata.modified_at,
                accessed_at=datetime.now(),
                mime_type=src_node.metadata.mime_type,
                encoding=src_node.metadata.encoding,
                language=src_node.metadata.language,
                permissions=src_node.metadata.permissions.copy(),
                custom_attributes=src_node.metadata.custom_attributes.copy()
            )
            
            # 复制版本历史
            if deep:
                new_node._versions = [
                    FileVersion(
                        version_id=v.version_id,
                        content=v.content,
                        timestamp=v.timestamp,
                        comment=v.comment,
                        checksum=v.checksum
                    )
                    for v in src_node._versions
                ]
        else:
            # 复制目录
            new_node = FileNode(new_name, FileType.DIRECTORY)
            new_node.metadata = FileMetadata(
                size=0,
                created_at=datetime.now(),
                modified_at=src_node.metadata.modified_at,
                accessed_at=datetime.now(),
                custom_attributes=src_node.metadata.custom_attributes.copy()
            )
            
            # 递归复制子节点
            if deep:
                for child in src_node.children.values():
                    copied_child = self._copy_node(child, child.name, deep)
                    new_node.add_child(copied_child)
        
        return new_node
    
    def _log_operation(self, operation: str, details: str) -> None:
        """记录操作日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "details": details
        }
        self._operation_history.append(log_entry)
        
        # 限制历史记录长度
        if len(self._operation_history) > 1000:
            self._operation_history = self._operation_history[-500:]
    
    def _collect_stats(self, node: FileNode, stats: Dict[str, Any], depth: int) -> None:
        """递归收集统计信息"""
        if node.is_file:
            stats["total_files"] += 1
            stats["total_size"] += node.metadata.size
            
            # 统计文件类型
            if node.metadata.language:
                lang = node.metadata.language
                stats["file_types"][lang] = stats["file_types"].get(lang, 0) + 1
        else:
            stats["total_directories"] += 1
            stats["max_depth"] = max(stats["max_depth"], depth)
            
            # 递归统计子节点
            for child in node.children.values():
                self._collect_stats(child, stats, depth + 1)


# 文件过滤和搜索功能将在后续子任务中实现
class FileFilter:
    """文件过滤器 - 待实现"""
    pass


class FileSearcher:
    """文件搜索引擎 - 待实现"""
    pass 