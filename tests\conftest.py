"""
pytest配置文件

包含所有测试的共享fixtures和配置
"""

import asyncio
import os
import sys
from pathlib import Path
from typing import AsyncGenerator, Generator

import pytest
import pytest_asyncio
from httpx import AsyncClient

# 添加app目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "app"))

# 测试环境配置
os.environ.update({
    "TESTING": "true",
    "DEBUG": "true",
    "DATABASE_URL": "sqlite:///./test.db",
    "OPENAI_API_KEY": "test-key",
    "LOG_LEVEL": "debug"
})


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def client() -> AsyncGenerator[AsyncClient, None]:
    """创建HTTP测试客户端"""
    from app.api_service import app
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def sample_python_code() -> str:
    """样例Python代码"""
    return '''
def fibonacci(n):
    """计算斐波那契数列"""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

class Calculator:
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def get_history(self):
        return self.history
'''


@pytest.fixture
def sample_javascript_code() -> str:
    """样例JavaScript代码"""
    return '''
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}

class Calculator {
    constructor() {
        this.history = [];
    }
    
    add(a, b) {
        const result = a + b;
        this.history.push(`${a} + ${b} = ${result}`);
        return result;
    }
    
    getHistory() {
        return this.history;
    }
}

export default Calculator;
'''


@pytest.fixture
def sample_file_structure() -> dict:
    """样例文件结构"""
    return {
        "src/": {
            "main.py": "print('Hello, World!')",
            "utils/": {
                "__init__.py": "",
                "helpers.py": "def helper(): pass",
            },
            "models/": {
                "__init__.py": "",
                "user.py": "class User: pass",
            }
        },
        "tests/": {
            "test_main.py": "def test_main(): assert True",
        },
        "README.md": "# Test Project",
        "requirements.txt": "pytest\nfastapi",
    }


@pytest.fixture
def mock_openai_response():
    """模拟OpenAI API响应"""
    return {
        "choices": [
            {
                "message": {
                    "content": "这是一个测试响应",
                    "role": "assistant"
                },
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": 10,
            "completion_tokens": 20,
            "total_tokens": 30
        }
    }


@pytest.fixture
def sample_diff() -> str:
    """样例diff字符串"""
    return '''--- a/src/main.py
+++ b/src/main.py
@@ -1,3 +1,4 @@
 def main():
+    print("Starting application...")
     print("Hello, World!")
     return 0
'''


# 测试数据清理
@pytest.fixture(autouse=True)
def cleanup_test_data():
    """自动清理测试数据"""
    yield
    # 清理测试文件
    test_files = [
        "test.db",
        "test.sqlite",
        "test_output.txt"
    ]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)


# 异步测试标记
pytest_asyncio.fixture(scope="session")
async def async_session():
    """异步会话fixture"""
    yield


# 性能测试配置
@pytest.fixture
def performance_threshold():
    """性能测试阈值配置"""
    return {
        "api_response_time": 1.0,  # 秒
        "file_processing_time": 0.5,  # 秒
        "search_time": 0.3,  # 秒
        "diff_application_time": 0.2,  # 秒
    } 