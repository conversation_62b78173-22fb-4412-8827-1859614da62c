#!/usr/bin/env python3
"""
性能优化脚本

针对大型项目进行性能调优，包括：
- 内存使用优化
- 索引构建优化
- 并发处理优化
- 缓存策略优化
"""

import os
import sys
import time
import psutil
import asyncio
import threading
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path
import json
import gc

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.vfs import VirtualFileSystem
from app.context_engine import CodeParser, SemanticSearcher, ContextBuilder


@dataclass
class PerformanceMetrics:
    """性能指标"""
    memory_usage_mb: float
    cpu_usage_percent: float
    execution_time_seconds: float
    file_count: int
    symbol_count: int
    index_size_mb: Optional[float] = None
    search_time_ms: Optional[float] = None


class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.vfs = None
        self.parser = None
        self.searcher = None
        self.context_builder = None
        self.metrics_history: List[PerformanceMetrics] = []
    
    def initialize_services(self):
        """初始化服务"""
        print("🚀 初始化服务...")
        
        start_time = time.time()
        
        self.vfs = VirtualFileSystem()
        self.parser = CodeParser()
        self.searcher = SemanticSearcher()
        self.context_builder = ContextBuilder(self.vfs, self.parser, self.searcher)
        
        init_time = time.time() - start_time
        print(f"✅ 服务初始化完成，耗时: {init_time:.2f}秒")
    
    def load_test_project(self, project_path: str) -> PerformanceMetrics:
        """加载测试项目"""
        print(f"📁 加载测试项目: {project_path}")
        
        start_time = time.time()
        start_memory = self.get_memory_usage()
        
        try:
            # 从真实文件系统导入项目
            if os.path.exists(project_path):
                success = self.vfs.import_from_filesystem(project_path, "/")
                if not success:
                    raise Exception("Failed to import project")
            else:
                # 创建模拟大型项目
                self._create_synthetic_project()
            
            # 获取项目统计信息
            stats = self.vfs.get_stats()
            file_count = stats.get('file_count', 0)
            
            end_time = time.time()
            end_memory = self.get_memory_usage()
            
            metrics = PerformanceMetrics(
                memory_usage_mb=end_memory - start_memory,
                cpu_usage_percent=psutil.cpu_percent(),
                execution_time_seconds=end_time - start_time,
                file_count=file_count,
                symbol_count=0  # 将在解析阶段更新
            )
            
            print(f"✅ 项目加载完成:")
            print(f"   - 文件数量: {metrics.file_count}")
            print(f"   - 内存使用: {metrics.memory_usage_mb:.1f} MB")
            print(f"   - 加载时间: {metrics.execution_time_seconds:.2f} 秒")
            
            return metrics
            
        except Exception as e:
            print(f"❌ 项目加载失败: {e}")
            raise
    
    def _create_synthetic_project(self):
        """创建合成大型项目用于测试"""
        print("🔧 创建合成测试项目...")
        
        # 创建多个Python文件
        for i in range(100):
            file_content = f'''
"""
模块 {i}: 数据处理工具
"""

import os
import json
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

@dataclass 
class DataItem{i}:
    """数据项 {i}"""
    id: int
    name: str
    value: float
    metadata: Dict[str, Any]

class DataProcessor{i}:
    """数据处理器 {i}"""
    
    def __init__(self):
        self.cache = {{}}
        self.stats = {{}}
    
    def process_data(self, items: List[DataItem{i}]) -> List[DataItem{i}]:
        """处理数据项列表"""
        processed = []
        for item in items:
            processed_item = self._transform_item(item)
            processed.append(processed_item)
        return processed
    
    def _transform_item(self, item: DataItem{i}) -> DataItem{i}:
        """转换单个数据项"""
        # 复杂的转换逻辑
        new_value = item.value * 1.1 + {i}
        new_metadata = item.metadata.copy()
        new_metadata['processed_at'] = time.time()
        new_metadata['processor_id'] = {i}
        
        return DataItem{i}(
            id=item.id,
            name=f"processed_{{item.name}}",
            value=new_value,
            metadata=new_metadata
        )
    
    def validate_data(self, items: List[DataItem{i}]) -> bool:
        """验证数据完整性"""
        for item in items:
            if not isinstance(item.id, int):
                return False
            if not isinstance(item.name, str):
                return False
            if not isinstance(item.value, (int, float)):
                return False
        return True
    
    def export_to_json(self, items: List[DataItem{i}], filepath: str):
        """导出数据到JSON文件"""
        data = []
        for item in items:
            data.append({{
                'id': item.id,
                'name': item.name,
                'value': item.value,
                'metadata': item.metadata
            }})
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)

def create_sample_data{i}() -> List[DataItem{i}]:
    """创建示例数据"""
    items = []
    for j in range(10):
        item = DataItem{i}(
            id=j,
            name=f"item_{{j}}",
            value=float(j * {i}),
            metadata={{'category': 'test', 'batch': {i}}}
        )
        items.append(item)
    return items

def main{i}():
    """主函数"""
    processor = DataProcessor{i}()
    sample_data = create_sample_data{i}()
    
    # 验证数据
    if not processor.validate_data(sample_data):
        print("数据验证失败")
        return
    
    # 处理数据
    processed_data = processor.process_data(sample_data)
    
    # 导出结果
    output_file = f"/tmp/processed_data_{i}.json"
    processor.export_to_json(processed_data, output_file)
    
    print(f"处理完成，结果已保存到 {{output_file}}")

if __name__ == "__main__":
    main{i}()
'''
            self.vfs.touch(f"/synthetic_project/modules/processor_{i}.py", file_content)
        
        # 创建一些JavaScript文件
        for i in range(50):
            js_content = f'''
/**
 * 前端组件 {i}
 */

import React, {{ useState, useEffect }} from 'react';
import {{ Button, Card, List }} from 'antd';

interface DataItem{i} {{
    id: number;
    name: string;
    value: number;
    metadata: Record<string, any>;
}}

const DataComponent{i}: React.FC = () => {{
    const [data, setData] = useState<DataItem{i}[]>([]);
    const [loading, setLoading] = useState(false);
    
    useEffect(() => {{
        loadData();
    }}, []);
    
    const loadData = async () => {{
        setLoading(true);
        try {{
            const response = await fetch(`/api/data/{i}`);
            const result = await response.json();
            setData(result);
        }} catch (error) {{
            console.error('加载数据失败:', error);
        }} finally {{
            setLoading(false);
        }}
    }};
    
    const handleProcessData = async () => {{
        setLoading(true);
        try {{
            const response = await fetch(`/api/process/{i}`, {{
                method: 'POST',
                headers: {{ 'Content-Type': 'application/json' }},
                body: JSON.stringify({{ data }})
            }});
            const result = await response.json();
            setData(result);
        }} catch (error) {{
            console.error('处理数据失败:', error);
        }} finally {{
            setLoading(false);
        }}
    }};
    
    const renderDataItem = (item: DataItem{i}) => (
        <List.Item key={{item.id}}>
            <Card size="small">
                <h4>{{item.name}}</h4>
                <p>值: {{item.value}}</p>
                <p>ID: {{item.id}}</p>
            </Card>
        </List.Item>
    );
    
    return (
        <div className="data-component-{i}">
            <h2>数据组件 {i}</h2>
            <div className="actions">
                <Button onClick={{loadData}} loading={{loading}}>
                    刷新数据
                </Button>
                <Button onClick={{handleProcessData}} loading={{loading}} type="primary">
                    处理数据
                </Button>
            </div>
            <List
                loading={{loading}}
                dataSource={{data}}
                renderItem={{renderDataItem}}
                pagination={{{{ pageSize: 10 }}}}
            />
        </div>
    );
}};

export default DataComponent{i};

export class DataService{i} {{
    private baseUrl: string;
    
    constructor(baseUrl: string = '/api') {{
        this.baseUrl = baseUrl;
    }}
    
    async fetchData(): Promise<DataItem{i}[]> {{
        const response = await fetch(`${{this.baseUrl}}/data/{i}`);
        return response.json();
    }}
    
    async processData(data: DataItem{i}[]): Promise<DataItem{i}[]> {{
        const response = await fetch(`${{this.baseUrl}}/process/{i}`, {{
            method: 'POST',
            headers: {{ 'Content-Type': 'application/json' }},
            body: JSON.stringify({{ data }})
        }});
        return response.json();
    }}
    
    async saveData(data: DataItem{i}[]): Promise<boolean> {{
        const response = await fetch(`${{this.baseUrl}}/save/{i}`, {{
            method: 'POST',
            headers: {{ 'Content-Type': 'application/json' }},
            body: JSON.stringify({{ data }})
        }});
        return response.ok;
    }}
}}
'''
            self.vfs.touch(f"/synthetic_project/frontend/components/DataComponent{i}.tsx", js_content)
        
        print(f"✅ 创建了150个合成文件用于性能测试")
    
    def analyze_code_symbols(self) -> PerformanceMetrics:
        """分析代码符号"""
        print("🔍 分析代码符号...")
        
        start_time = time.time()
        start_memory = self.get_memory_usage()
        
        all_symbols = []
        file_count = 0
        
        # 遍历所有文件
        for path_info in self.vfs.walk():
            file_path = path_info[0]
            if self.vfs.is_file(file_path):
                try:
                    content = self.vfs.read_file(file_path, encoding='utf-8')
                    if isinstance(content, bytes):
                        content = content.decode('utf-8')
                    
                    symbols = self.parser.extract_symbols(file_path, content)
                    all_symbols.extend(symbols)
                    file_count += 1
                    
                    # 每处理100个文件显示进度
                    if file_count % 100 == 0:
                        print(f"   已处理 {file_count} 个文件...")
                        
                except Exception as e:
                    print(f"   警告: 处理文件 {file_path} 时出错: {e}")
        
        end_time = time.time()
        end_memory = self.get_memory_usage()
        
        metrics = PerformanceMetrics(
            memory_usage_mb=end_memory - start_memory,
            cpu_usage_percent=psutil.cpu_percent(),
            execution_time_seconds=end_time - start_time,
            file_count=file_count,
            symbol_count=len(all_symbols)
        )
        
        print(f"✅ 符号分析完成:")
        print(f"   - 处理文件: {metrics.file_count}")
        print(f"   - 提取符号: {metrics.symbol_count}")
        print(f"   - 内存使用: {metrics.memory_usage_mb:.1f} MB")
        print(f"   - 分析时间: {metrics.execution_time_seconds:.2f} 秒")
        
        # 存储符号供后续使用
        self._all_symbols = all_symbols
        
        return metrics
    
    def build_search_index(self) -> PerformanceMetrics:
        """构建搜索索引"""
        print("🏗️ 构建搜索索引...")
        
        if not hasattr(self, '_all_symbols'):
            print("❌ 请先运行符号分析")
            return None
        
        start_time = time.time()
        start_memory = self.get_memory_usage()
        
        # 构建索引
        self.searcher.build_index(self._all_symbols)
        
        end_time = time.time()
        end_memory = self.get_memory_usage()
        
        # 计算索引大小
        index_size_mb = 0
        if hasattr(self.searcher, 'embeddings') and self.searcher.embeddings is not None:
            # 估算嵌入向量的内存大小
            index_size_mb = self.searcher.embeddings.nbytes / (1024 * 1024)
        
        metrics = PerformanceMetrics(
            memory_usage_mb=end_memory - start_memory,
            cpu_usage_percent=psutil.cpu_percent(),
            execution_time_seconds=end_time - start_time,
            file_count=0,
            symbol_count=len(self._all_symbols),
            index_size_mb=index_size_mb
        )
        
        print(f"✅ 索引构建完成:")
        print(f"   - 索引符号: {metrics.symbol_count}")
        print(f"   - 索引大小: {metrics.index_size_mb:.1f} MB")
        print(f"   - 内存使用: {metrics.memory_usage_mb:.1f} MB")
        print(f"   - 构建时间: {metrics.execution_time_seconds:.2f} 秒")
        
        return metrics
    
    def benchmark_search_performance(self) -> PerformanceMetrics:
        """基准测试搜索性能"""
        print("⚡ 基准测试搜索性能...")
        
        if self.searcher.model is None:
            print("⚠️ 语义搜索模型不可用，跳过搜索测试")
            return PerformanceMetrics(0, 0, 0, 0, 0, search_time_ms=0)
        
        # 测试查询
        test_queries = [
            "data processing function",
            "file validation method",
            "JSON export utility",
            "React component state",
            "async data loading",
            "error handling logic",
            "cache management",
            "user interface component",
            "database connection",
            "API endpoint handler"
        ]
        
        total_time = 0
        search_count = 0
        
        start_memory = self.get_memory_usage()
        
        for query in test_queries:
            start_time = time.time()
            
            try:
                results = self.searcher.search(query, top_k=10, min_score=0.3)
                end_time = time.time()
                
                search_time = (end_time - start_time) * 1000  # 转换为毫秒
                total_time += search_time
                search_count += 1
                
                print(f"   查询 '{query}': {search_time:.1f}ms, {len(results)} 结果")
                
            except Exception as e:
                print(f"   查询 '{query}' 失败: {e}")
        
        end_memory = self.get_memory_usage()
        avg_search_time = total_time / search_count if search_count > 0 else 0
        
        metrics = PerformanceMetrics(
            memory_usage_mb=end_memory - start_memory,
            cpu_usage_percent=psutil.cpu_percent(),
            execution_time_seconds=total_time / 1000,  # 转换为秒
            file_count=0,
            symbol_count=0,
            search_time_ms=avg_search_time
        )
        
        print(f"✅ 搜索性能测试完成:")
        print(f"   - 平均搜索时间: {metrics.search_time_ms:.1f} ms")
        print(f"   - 总查询次数: {search_count}")
        print(f"   - 内存使用: {metrics.memory_usage_mb:.1f} MB")
        
        return metrics
    
    def optimize_memory_usage(self):
        """优化内存使用"""
        print("🧹 优化内存使用...")
        
        before_memory = self.get_memory_usage()
        
        # 1. 强制垃圾回收
        gc.collect()
        
        # 2. 清理缓存
        if hasattr(self.context_builder, '_symbol_cache'):
            cache_size = len(self.context_builder._symbol_cache)
            self.context_builder._symbol_cache.clear()
            print(f"   清理符号缓存: {cache_size} 项")
        
        # 3. 优化VFS存储
        if hasattr(self.vfs, '_compact_storage'):
            self.vfs._compact_storage()
            print("   压缩VFS存储")
        
        after_memory = self.get_memory_usage()
        memory_saved = before_memory - after_memory
        
        print(f"✅ 内存优化完成:")
        print(f"   - 优化前: {before_memory:.1f} MB")
        print(f"   - 优化后: {after_memory:.1f} MB") 
        print(f"   - 节省内存: {memory_saved:.1f} MB")
    
    def benchmark_concurrent_operations(self) -> PerformanceMetrics:
        """基准测试并发操作"""
        print("🔄 基准测试并发操作...")
        
        start_time = time.time()
        start_memory = self.get_memory_usage()
        
        # 创建多个并发任务
        tasks = []
        results = []
        
        def search_task(query: str, task_id: int):
            """搜索任务"""
            try:
                if self.searcher.model is not None:
                    task_results = self.searcher.search(f"{query} {task_id}", top_k=5)
                    results.append({"task_id": task_id, "results": len(task_results)})
                else:
                    results.append({"task_id": task_id, "results": 0})
            except Exception as e:
                results.append({"task_id": task_id, "error": str(e)})
        
        # 启动10个并发搜索任务
        threads = []
        for i in range(10):
            thread = threading.Thread(target=search_task, args=(f"function", i))
            threads.append(thread)
            thread.start()
        
        # 等待所有任务完成
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        end_memory = self.get_memory_usage()
        
        successful_tasks = len([r for r in results if "error" not in r])
        
        metrics = PerformanceMetrics(
            memory_usage_mb=end_memory - start_memory,
            cpu_usage_percent=psutil.cpu_percent(),
            execution_time_seconds=end_time - start_time,
            file_count=successful_tasks,
            symbol_count=sum(r.get("results", 0) for r in results)
        )
        
        print(f"✅ 并发测试完成:")
        print(f"   - 成功任务: {successful_tasks}/10")
        print(f"   - 总执行时间: {metrics.execution_time_seconds:.2f} 秒")
        print(f"   - 内存使用: {metrics.memory_usage_mb:.1f} MB")
        
        return metrics
    
    def get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        process = psutil.Process()
        return process.memory_info().rss / (1024 * 1024)
    
    def generate_performance_report(self):
        """生成性能报告"""
        print("\n" + "="*60)
        print("📊 性能优化报告")
        print("="*60)
        
        if not self.metrics_history:
            print("❌ 没有性能数据可报告")
            return
        
        # 计算总体统计
        total_time = sum(m.execution_time_seconds for m in self.metrics_history)
        total_memory = sum(m.memory_usage_mb for m in self.metrics_history)
        max_files = max((m.file_count for m in self.metrics_history), default=0)
        max_symbols = max((m.symbol_count for m in self.metrics_history), default=0)
        
        print(f"📈 总体统计:")
        print(f"   - 总执行时间: {total_time:.2f} 秒")
        print(f"   - 峰值内存使用: {max(m.memory_usage_mb for m in self.metrics_history):.1f} MB")
        print(f"   - 处理文件数: {max_files}")
        print(f"   - 提取符号数: {max_symbols}")
        
        # 搜索性能统计
        search_metrics = [m for m in self.metrics_history if m.search_time_ms is not None]
        if search_metrics:
            avg_search_time = sum(m.search_time_ms for m in search_metrics) / len(search_metrics)
            print(f"   - 平均搜索时间: {avg_search_time:.1f} ms")
        
        # 优化建议
        print(f"\n💡 优化建议:")
        
        if total_memory > 1000:  # 1GB
            print("   - 内存使用较高，建议启用内存优化")
        
        if max_symbols > 10000:
            print("   - 符号数量较多，建议使用增量索引")
        
        if any(m.execution_time_seconds > 30 for m in self.metrics_history):
            print("   - 部分操作耗时较长，建议使用并行处理")
        
        # 保存报告到文件
        report_data = {
            "timestamp": time.time(),
            "metrics": [
                {
                    "memory_usage_mb": m.memory_usage_mb,
                    "cpu_usage_percent": m.cpu_usage_percent,
                    "execution_time_seconds": m.execution_time_seconds,
                    "file_count": m.file_count,
                    "symbol_count": m.symbol_count,
                    "index_size_mb": m.index_size_mb,
                    "search_time_ms": m.search_time_ms
                }
                for m in self.metrics_history
            ],
            "summary": {
                "total_time": total_time,
                "peak_memory": max(m.memory_usage_mb for m in self.metrics_history),
                "max_files": max_files,
                "max_symbols": max_symbols
            }
        }
        
        report_file = "performance_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
    
    def run_full_benchmark(self, project_path: str = None):
        """运行完整的性能基准测试"""
        print("🚀 开始完整性能基准测试")
        print("="*60)
        
        try:
            # 1. 初始化服务
            self.initialize_services()
            
            # 2. 加载项目
            metrics = self.load_test_project(project_path or ".")
            self.metrics_history.append(metrics)
            
            # 3. 分析代码符号
            metrics = self.analyze_code_symbols()
            self.metrics_history.append(metrics)
            
            # 4. 构建搜索索引
            metrics = self.build_search_index()
            self.metrics_history.append(metrics)
            
            # 5. 搜索性能测试
            metrics = self.benchmark_search_performance()
            self.metrics_history.append(metrics)
            
            # 6. 并发操作测试
            metrics = self.benchmark_concurrent_operations()
            self.metrics_history.append(metrics)
            
            # 7. 内存优化
            self.optimize_memory_usage()
            
            # 8. 生成报告
            self.generate_performance_report()
            
            print("\n🎉 性能基准测试完成！")
            
        except Exception as e:
            print(f"\n❌ 性能测试失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AI编程工具性能优化器")
    parser.add_argument("--project", "-p", help="要测试的项目路径")
    parser.add_argument("--memory-only", action="store_true", help="仅运行内存优化")
    parser.add_argument("--benchmark-only", action="store_true", help="仅运行性能基准测试")
    
    args = parser.parse_args()
    
    optimizer = PerformanceOptimizer()
    
    if args.memory_only:
        optimizer.initialize_services()
        optimizer.optimize_memory_usage()
    elif args.benchmark_only:
        optimizer.run_full_benchmark(args.project)
    else:
        optimizer.run_full_benchmark(args.project)


if __name__ == "__main__":
    main()
"""
性能优化脚本

针对大型项目进行性能调优，包括：
- 内存使用优化
- 索引构建优化
- 并发处理优化
- 缓存策略优化
"""

import os
import sys
import time
import psutil
import asyncio
import threading
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path
import json
import gc

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.vfs import VirtualFileSystem
from app.context_engine import CodeParser, SemanticSearcher, ContextBuilder


@dataclass
class PerformanceMetrics:
    """性能指标"""
    memory_usage_mb: float
    cpu_usage_percent: float
    execution_time_seconds: float
    file_count: int
    symbol_count: int
    index_size_mb: Optional[float] = None
    search_time_ms: Optional[float] = None


class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.vfs = None
        self.parser = None
        self.searcher = None
        self.context_builder = None
        self.metrics_history: List[PerformanceMetrics] = []
    
    def initialize_services(self):
        """初始化服务"""
        print("🚀 初始化服务...")
        
        start_time = time.time()
        
        self.vfs = VirtualFileSystem()
        self.parser = CodeParser()
        self.searcher = SemanticSearcher()
        self.context_builder = ContextBuilder(self.vfs, self.parser, self.searcher)
        
        init_time = time.time() - start_time
        print(f"✅ 服务初始化完成，耗时: {init_time:.2f}秒")
    
    def load_test_project(self, project_path: str) -> PerformanceMetrics:
        """加载测试项目"""
        print(f"📁 加载测试项目: {project_path}")
        
        start_time = time.time()
        start_memory = self.get_memory_usage()
        
        try:
            # 从真实文件系统导入项目
            if os.path.exists(project_path):
                success = self.vfs.import_from_filesystem(project_path, "/")
                if not success:
                    raise Exception("Failed to import project")
            else:
                # 创建模拟大型项目
                self._create_synthetic_project()
            
            # 获取项目统计信息
            stats = self.vfs.get_stats()
            file_count = stats.get('file_count', 0)
            
            end_time = time.time()
            end_memory = self.get_memory_usage()
            
            metrics = PerformanceMetrics(
                memory_usage_mb=end_memory - start_memory,
                cpu_usage_percent=psutil.cpu_percent(),
                execution_time_seconds=end_time - start_time,
                file_count=file_count,
                symbol_count=0  # 将在解析阶段更新
            )
            
            print(f"✅ 项目加载完成:")
            print(f"   - 文件数量: {metrics.file_count}")
            print(f"   - 内存使用: {metrics.memory_usage_mb:.1f} MB")
            print(f"   - 加载时间: {metrics.execution_time_seconds:.2f} 秒")
            
            return metrics
            
        except Exception as e:
            print(f"❌ 项目加载失败: {e}")
            raise
    
    def _create_synthetic_project(self):
        """创建合成大型项目用于测试"""
        print("🔧 创建合成测试项目...")
        
        # 创建多个Python文件
        for i in range(100):
            file_content = f'''
"""
模块 {i}: 数据处理工具
"""

import os
import json
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

@dataclass 
class DataItem{i}:
    """数据项 {i}"""
    id: int
    name: str
    value: float
    metadata: Dict[str, Any]

class DataProcessor{i}:
    """数据处理器 {i}"""
    
    def __init__(self):
        self.cache = {{}}
        self.stats = {{}}
    
    def process_data(self, items: List[DataItem{i}]) -> List[DataItem{i}]:
        """处理数据项列表"""
        processed = []
        for item in items:
            processed_item = self._transform_item(item)
            processed.append(processed_item)
        return processed
    
    def _transform_item(self, item: DataItem{i}) -> DataItem{i}:
        """转换单个数据项"""
        # 复杂的转换逻辑
        new_value = item.value * 1.1 + {i}
        new_metadata = item.metadata.copy()
        new_metadata['processed_at'] = time.time()
        new_metadata['processor_id'] = {i}
        
        return DataItem{i}(
            id=item.id,
            name=f"processed_{{item.name}}",
            value=new_value,
            metadata=new_metadata
        )
    
    def validate_data(self, items: List[DataItem{i}]) -> bool:
        """验证数据完整性"""
        for item in items:
            if not isinstance(item.id, int):
                return False
            if not isinstance(item.name, str):
                return False
            if not isinstance(item.value, (int, float)):
                return False
        return True
    
    def export_to_json(self, items: List[DataItem{i}], filepath: str):
        """导出数据到JSON文件"""
        data = []
        for item in items:
            data.append({{
                'id': item.id,
                'name': item.name,
                'value': item.value,
                'metadata': item.metadata
            }})
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)

def create_sample_data{i}() -> List[DataItem{i}]:
    """创建示例数据"""
    items = []
    for j in range(10):
        item = DataItem{i}(
            id=j,
            name=f"item_{{j}}",
            value=float(j * {i}),
            metadata={{'category': 'test', 'batch': {i}}}
        )
        items.append(item)
    return items

def main{i}():
    """主函数"""
    processor = DataProcessor{i}()
    sample_data = create_sample_data{i}()
    
    # 验证数据
    if not processor.validate_data(sample_data):
        print("数据验证失败")
        return
    
    # 处理数据
    processed_data = processor.process_data(sample_data)
    
    # 导出结果
    output_file = f"/tmp/processed_data_{i}.json"
    processor.export_to_json(processed_data, output_file)
    
    print(f"处理完成，结果已保存到 {{output_file}}")

if __name__ == "__main__":
    main{i}()
'''
            self.vfs.touch(f"/synthetic_project/modules/processor_{i}.py", file_content)
        
        # 创建一些JavaScript文件
        for i in range(50):
            js_content = f'''
/**
 * 前端组件 {i}
 */

import React, {{ useState, useEffect }} from 'react';
import {{ Button, Card, List }} from 'antd';

interface DataItem{i} {{
    id: number;
    name: string;
    value: number;
    metadata: Record<string, any>;
}}

const DataComponent{i}: React.FC = () => {{
    const [data, setData] = useState<DataItem{i}[]>([]);
    const [loading, setLoading] = useState(false);
    
    useEffect(() => {{
        loadData();
    }}, []);
    
    const loadData = async () => {{
        setLoading(true);
        try {{
            const response = await fetch(`/api/data/{i}`);
            const result = await response.json();
            setData(result);
        }} catch (error) {{
            console.error('加载数据失败:', error);
        }} finally {{
            setLoading(false);
        }}
    }};
    
    const handleProcessData = async () => {{
        setLoading(true);
        try {{
            const response = await fetch(`/api/process/{i}`, {{
                method: 'POST',
                headers: {{ 'Content-Type': 'application/json' }},
                body: JSON.stringify({{ data }})
            }});
            const result = await response.json();
            setData(result);
        }} catch (error) {{
            console.error('处理数据失败:', error);
        }} finally {{
            setLoading(false);
        }}
    }};
    
    const renderDataItem = (item: DataItem{i}) => (
        <List.Item key={{item.id}}>
            <Card size="small">
                <h4>{{item.name}}</h4>
                <p>值: {{item.value}}</p>
                <p>ID: {{item.id}}</p>
            </Card>
        </List.Item>
    );
    
    return (
        <div className="data-component-{i}">
            <h2>数据组件 {i}</h2>
            <div className="actions">
                <Button onClick={{loadData}} loading={{loading}}>
                    刷新数据
                </Button>
                <Button onClick={{handleProcessData}} loading={{loading}} type="primary">
                    处理数据
                </Button>
            </div>
            <List
                loading={{loading}}
                dataSource={{data}}
                renderItem={{renderDataItem}}
                pagination={{{{ pageSize: 10 }}}}
            />
        </div>
    );
}};

export default DataComponent{i};

export class DataService{i} {{
    private baseUrl: string;
    
    constructor(baseUrl: string = '/api') {{
        this.baseUrl = baseUrl;
    }}
    
    async fetchData(): Promise<DataItem{i}[]> {{
        const response = await fetch(`${{this.baseUrl}}/data/{i}`);
        return response.json();
    }}
    
    async processData(data: DataItem{i}[]): Promise<DataItem{i}[]> {{
        const response = await fetch(`${{this.baseUrl}}/process/{i}`, {{
            method: 'POST',
            headers: {{ 'Content-Type': 'application/json' }},
            body: JSON.stringify({{ data }})
        }});
        return response.json();
    }}
    
    async saveData(data: DataItem{i}[]): Promise<boolean> {{
        const response = await fetch(`${{this.baseUrl}}/save/{i}`, {{
            method: 'POST',
            headers: {{ 'Content-Type': 'application/json' }},
            body: JSON.stringify({{ data }})
        }});
        return response.ok;
    }}
}}
'''
            self.vfs.touch(f"/synthetic_project/frontend/components/DataComponent{i}.tsx", js_content)
        
        print(f"✅ 创建了150个合成文件用于性能测试")
    
    def analyze_code_symbols(self) -> PerformanceMetrics:
        """分析代码符号"""
        print("🔍 分析代码符号...")
        
        start_time = time.time()
        start_memory = self.get_memory_usage()
        
        all_symbols = []
        file_count = 0
        
        # 遍历所有文件
        for path_info in self.vfs.walk():
            file_path = path_info[0]
            if self.vfs.is_file(file_path):
                try:
                    content = self.vfs.read_file(file_path, encoding='utf-8')
                    if isinstance(content, bytes):
                        content = content.decode('utf-8')
                    
                    symbols = self.parser.extract_symbols(file_path, content)
                    all_symbols.extend(symbols)
                    file_count += 1
                    
                    # 每处理100个文件显示进度
                    if file_count % 100 == 0:
                        print(f"   已处理 {file_count} 个文件...")
                        
                except Exception as e:
                    print(f"   警告: 处理文件 {file_path} 时出错: {e}")
        
        end_time = time.time()
        end_memory = self.get_memory_usage()
        
        metrics = PerformanceMetrics(
            memory_usage_mb=end_memory - start_memory,
            cpu_usage_percent=psutil.cpu_percent(),
            execution_time_seconds=end_time - start_time,
            file_count=file_count,
            symbol_count=len(all_symbols)
        )
        
        print(f"✅ 符号分析完成:")
        print(f"   - 处理文件: {metrics.file_count}")
        print(f"   - 提取符号: {metrics.symbol_count}")
        print(f"   - 内存使用: {metrics.memory_usage_mb:.1f} MB")
        print(f"   - 分析时间: {metrics.execution_time_seconds:.2f} 秒")
        
        # 存储符号供后续使用
        self._all_symbols = all_symbols
        
        return metrics
    
    def build_search_index(self) -> PerformanceMetrics:
        """构建搜索索引"""
        print("🏗️ 构建搜索索引...")
        
        if not hasattr(self, '_all_symbols'):
            print("❌ 请先运行符号分析")
            return None
        
        start_time = time.time()
        start_memory = self.get_memory_usage()
        
        # 构建索引
        self.searcher.build_index(self._all_symbols)
        
        end_time = time.time()
        end_memory = self.get_memory_usage()
        
        # 计算索引大小
        index_size_mb = 0
        if hasattr(self.searcher, 'embeddings') and self.searcher.embeddings is not None:
            # 估算嵌入向量的内存大小
            index_size_mb = self.searcher.embeddings.nbytes / (1024 * 1024)
        
        metrics = PerformanceMetrics(
            memory_usage_mb=end_memory - start_memory,
            cpu_usage_percent=psutil.cpu_percent(),
            execution_time_seconds=end_time - start_time,
            file_count=0,
            symbol_count=len(self._all_symbols),
            index_size_mb=index_size_mb
        )
        
        print(f"✅ 索引构建完成:")
        print(f"   - 索引符号: {metrics.symbol_count}")
        print(f"   - 索引大小: {metrics.index_size_mb:.1f} MB")
        print(f"   - 内存使用: {metrics.memory_usage_mb:.1f} MB")
        print(f"   - 构建时间: {metrics.execution_time_seconds:.2f} 秒")
        
        return metrics
    
    def benchmark_search_performance(self) -> PerformanceMetrics:
        """基准测试搜索性能"""
        print("⚡ 基准测试搜索性能...")
        
        if self.searcher.model is None:
            print("⚠️ 语义搜索模型不可用，跳过搜索测试")
            return PerformanceMetrics(0, 0, 0, 0, 0, search_time_ms=0)
        
        # 测试查询
        test_queries = [
            "data processing function",
            "file validation method",
            "JSON export utility",
            "React component state",
            "async data loading",
            "error handling logic",
            "cache management",
            "user interface component",
            "database connection",
            "API endpoint handler"
        ]
        
        total_time = 0
        search_count = 0
        
        start_memory = self.get_memory_usage()
        
        for query in test_queries:
            start_time = time.time()
            
            try:
                results = self.searcher.search(query, top_k=10, min_score=0.3)
                end_time = time.time()
                
                search_time = (end_time - start_time) * 1000  # 转换为毫秒
                total_time += search_time
                search_count += 1
                
                print(f"   查询 '{query}': {search_time:.1f}ms, {len(results)} 结果")
                
            except Exception as e:
                print(f"   查询 '{query}' 失败: {e}")
        
        end_memory = self.get_memory_usage()
        avg_search_time = total_time / search_count if search_count > 0 else 0
        
        metrics = PerformanceMetrics(
            memory_usage_mb=end_memory - start_memory,
            cpu_usage_percent=psutil.cpu_percent(),
            execution_time_seconds=total_time / 1000,  # 转换为秒
            file_count=0,
            symbol_count=0,
            search_time_ms=avg_search_time
        )
        
        print(f"✅ 搜索性能测试完成:")
        print(f"   - 平均搜索时间: {metrics.search_time_ms:.1f} ms")
        print(f"   - 总查询次数: {search_count}")
        print(f"   - 内存使用: {metrics.memory_usage_mb:.1f} MB")
        
        return metrics
    
    def optimize_memory_usage(self):
        """优化内存使用"""
        print("🧹 优化内存使用...")
        
        before_memory = self.get_memory_usage()
        
        # 1. 强制垃圾回收
        gc.collect()
        
        # 2. 清理缓存
        if hasattr(self.context_builder, '_symbol_cache'):
            cache_size = len(self.context_builder._symbol_cache)
            self.context_builder._symbol_cache.clear()
            print(f"   清理符号缓存: {cache_size} 项")
        
        # 3. 优化VFS存储
        if hasattr(self.vfs, '_compact_storage'):
            self.vfs._compact_storage()
            print("   压缩VFS存储")
        
        after_memory = self.get_memory_usage()
        memory_saved = before_memory - after_memory
        
        print(f"✅ 内存优化完成:")
        print(f"   - 优化前: {before_memory:.1f} MB")
        print(f"   - 优化后: {after_memory:.1f} MB") 
        print(f"   - 节省内存: {memory_saved:.1f} MB")
    
    def benchmark_concurrent_operations(self) -> PerformanceMetrics:
        """基准测试并发操作"""
        print("🔄 基准测试并发操作...")
        
        start_time = time.time()
        start_memory = self.get_memory_usage()
        
        # 创建多个并发任务
        tasks = []
        results = []
        
        def search_task(query: str, task_id: int):
            """搜索任务"""
            try:
                if self.searcher.model is not None:
                    task_results = self.searcher.search(f"{query} {task_id}", top_k=5)
                    results.append({"task_id": task_id, "results": len(task_results)})
                else:
                    results.append({"task_id": task_id, "results": 0})
            except Exception as e:
                results.append({"task_id": task_id, "error": str(e)})
        
        # 启动10个并发搜索任务
        threads = []
        for i in range(10):
            thread = threading.Thread(target=search_task, args=(f"function", i))
            threads.append(thread)
            thread.start()
        
        # 等待所有任务完成
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        end_memory = self.get_memory_usage()
        
        successful_tasks = len([r for r in results if "error" not in r])
        
        metrics = PerformanceMetrics(
            memory_usage_mb=end_memory - start_memory,
            cpu_usage_percent=psutil.cpu_percent(),
            execution_time_seconds=end_time - start_time,
            file_count=successful_tasks,
            symbol_count=sum(r.get("results", 0) for r in results)
        )
        
        print(f"✅ 并发测试完成:")
        print(f"   - 成功任务: {successful_tasks}/10")
        print(f"   - 总执行时间: {metrics.execution_time_seconds:.2f} 秒")
        print(f"   - 内存使用: {metrics.memory_usage_mb:.1f} MB")
        
        return metrics
    
    def get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        process = psutil.Process()
        return process.memory_info().rss / (1024 * 1024)
    
    def generate_performance_report(self):
        """生成性能报告"""
        print("\n" + "="*60)
        print("📊 性能优化报告")
        print("="*60)
        
        if not self.metrics_history:
            print("❌ 没有性能数据可报告")
            return
        
        # 计算总体统计
        total_time = sum(m.execution_time_seconds for m in self.metrics_history)
        total_memory = sum(m.memory_usage_mb for m in self.metrics_history)
        max_files = max((m.file_count for m in self.metrics_history), default=0)
        max_symbols = max((m.symbol_count for m in self.metrics_history), default=0)
        
        print(f"📈 总体统计:")
        print(f"   - 总执行时间: {total_time:.2f} 秒")
        print(f"   - 峰值内存使用: {max(m.memory_usage_mb for m in self.metrics_history):.1f} MB")
        print(f"   - 处理文件数: {max_files}")
        print(f"   - 提取符号数: {max_symbols}")
        
        # 搜索性能统计
        search_metrics = [m for m in self.metrics_history if m.search_time_ms is not None]
        if search_metrics:
            avg_search_time = sum(m.search_time_ms for m in search_metrics) / len(search_metrics)
            print(f"   - 平均搜索时间: {avg_search_time:.1f} ms")
        
        # 优化建议
        print(f"\n💡 优化建议:")
        
        if total_memory > 1000:  # 1GB
            print("   - 内存使用较高，建议启用内存优化")
        
        if max_symbols > 10000:
            print("   - 符号数量较多，建议使用增量索引")
        
        if any(m.execution_time_seconds > 30 for m in self.metrics_history):
            print("   - 部分操作耗时较长，建议使用并行处理")
        
        # 保存报告到文件
        report_data = {
            "timestamp": time.time(),
            "metrics": [
                {
                    "memory_usage_mb": m.memory_usage_mb,
                    "cpu_usage_percent": m.cpu_usage_percent,
                    "execution_time_seconds": m.execution_time_seconds,
                    "file_count": m.file_count,
                    "symbol_count": m.symbol_count,
                    "index_size_mb": m.index_size_mb,
                    "search_time_ms": m.search_time_ms
                }
                for m in self.metrics_history
            ],
            "summary": {
                "total_time": total_time,
                "peak_memory": max(m.memory_usage_mb for m in self.metrics_history),
                "max_files": max_files,
                "max_symbols": max_symbols
            }
        }
        
        report_file = "performance_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
    
    def run_full_benchmark(self, project_path: str = None):
        """运行完整的性能基准测试"""
        print("🚀 开始完整性能基准测试")
        print("="*60)
        
        try:
            # 1. 初始化服务
            self.initialize_services()
            
            # 2. 加载项目
            metrics = self.load_test_project(project_path or ".")
            self.metrics_history.append(metrics)
            
            # 3. 分析代码符号
            metrics = self.analyze_code_symbols()
            self.metrics_history.append(metrics)
            
            # 4. 构建搜索索引
            metrics = self.build_search_index()
            self.metrics_history.append(metrics)
            
            # 5. 搜索性能测试
            metrics = self.benchmark_search_performance()
            self.metrics_history.append(metrics)
            
            # 6. 并发操作测试
            metrics = self.benchmark_concurrent_operations()
            self.metrics_history.append(metrics)
            
            # 7. 内存优化
            self.optimize_memory_usage()
            
            # 8. 生成报告
            self.generate_performance_report()
            
            print("\n🎉 性能基准测试完成！")
            
        except Exception as e:
            print(f"\n❌ 性能测试失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AI编程工具性能优化器")
    parser.add_argument("--project", "-p", help="要测试的项目路径")
    parser.add_argument("--memory-only", action="store_true", help="仅运行内存优化")
    parser.add_argument("--benchmark-only", action="store_true", help="仅运行性能基准测试")
    
    args = parser.parse_args()
    
    optimizer = PerformanceOptimizer()
    
    if args.memory_only:
        optimizer.initialize_services()
        optimizer.optimize_memory_usage()
    elif args.benchmark_only:
        optimizer.run_full_benchmark(args.project)
    else:
        optimizer.run_full_benchmark(args.project)


if __name__ == "__main__":
    main() 