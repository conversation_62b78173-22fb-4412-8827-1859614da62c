"""
API集成测试

测试后端API服务的各个端点和集成功能
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, patch
from httpx import AsyncClient
from fastapi.testclient import TestClient

from app.api_service import app
from app.vfs import VirtualFileSystem


@pytest.fixture
def client():
    """测试客户端"""
    return TestClient(app)


@pytest.fixture
async def async_client():
    """异步测试客户端"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


class TestHealthEndpoint:
    """健康检查端点测试"""
    
    def test_health_check(self, client):
        """测试健康检查端点"""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "services" in data
        assert data["status"] == "healthy"


class TestFileSystemAPI:
    """文件系统API测试"""
    
    def test_get_file_tree_empty(self, client):
        """测试获取空文件树"""
        response = client.get("/api/files/tree")
        assert response.status_code in [200, 503]  # 可能因为服务未初始化而返回503
    
    def test_create_file(self, client):
        """测试创建文件"""
        file_data = {
            "path": "/test.py",
            "content": "print('Hello, World!')",
            "encoding": "utf-8"
        }
        
        response = client.post("/api/files/create", json=file_data)
        
        # 可能因为服务未初始化而失败，这是正常的
        assert response.status_code in [200, 503]
        
        if response.status_code == 200:
            data = response.json()
            assert "success" in data
            assert "message" in data
    
    def test_get_file_content_not_found(self, client):
        """测试获取不存在文件的内容"""
        response = client.get("/api/files/content?path=/nonexistent.py")
        
        # 可能因为服务未初始化而返回503，或者文件不存在返回404
        assert response.status_code in [404, 503]
    
    def test_delete_file(self, client):
        """测试删除文件"""
        response = client.delete("/api/files/delete?path=/test.py")
        
        # 可能因为服务未初始化而失败
        assert response.status_code in [200, 503]


class TestSearchAPI:
    """搜索API测试"""
    
    def test_semantic_search_empty_query(self, client):
        """测试空查询的语义搜索"""
        search_data = {
            "query": "",
            "max_results": 10,
            "min_score": 0.5
        }
        
        response = client.post("/api/search/semantic", json=search_data)
        
        # 可能因为服务未初始化而返回503
        assert response.status_code in [200, 503]
    
    def test_context_search(self, client):
        """测试上下文搜索"""
        context_data = {
            "query": "test function",
            "focus_files": ["/test.py"],
            "max_files": 5,
            "max_symbols": 20
        }
        
        response = client.post("/api/search/context", json=context_data)
        
        # 可能因为服务未初始化而返回503
        assert response.status_code in [200, 503]
    
    def test_rebuild_search_index(self, client):
        """测试重建搜索索引"""
        response = client.post("/api/search/rebuild_index")
        
        # 可能因为服务未初始化而返回503
        assert response.status_code in [200, 503]


class TestAIAPI:
    """AI API测试"""
    
    @patch('app.api_service.ai_client')
    def test_ai_chat_without_context(self, mock_ai_client, client):
        """测试不带上下文的AI对话"""
        # 模拟AI客户端
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Hello! How can I help you?"
        mock_response.usage = Mock()
        mock_response.usage.prompt_tokens = 10
        mock_response.usage.completion_tokens = 5
        mock_response.usage.total_tokens = 15
        
        mock_ai_client.chat.completions.create.return_value = mock_response
        
        chat_data = {
            "message": "Hello, AI!",
            "model": "gpt-3.5-turbo"
        }
        
        response = client.post("/api/ai/chat", json=chat_data)
        
        # 如果AI客户端未初始化，会返回503
        assert response.status_code in [200, 503]
        
        if response.status_code == 200:
            data = response.json()
            assert "response" in data
            assert "conversation_id" in data
            assert "model" in data
    
    @patch('app.api_service.ai_client')
    def test_ai_chat_with_context(self, mock_ai_client, client):
        """测试带上下文的AI对话"""
        # 模拟AI客户端
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Based on the context, this function processes data..."
        mock_response.usage = None
        
        mock_ai_client.chat.completions.create.return_value = mock_response
        
        context_data = {
            "primary_file": "/test.py",
            "primary_symbols": [
                {
                    "symbol_name": "process_data",
                    "symbol_type": "function",
                    "file_path": "/test.py",
                    "line_start": 1,
                    "line_end": 5,
                    "definition": "def process_data(data):\n    return data",
                    "score": 0.9
                }
            ],
            "related_files": [],
            "related_symbols": [],
            "imports": [],
            "exports": [],
            "dependencies": {},
            "context_score": 0.8
        }
        
        chat_data = {
            "message": "What does this function do?",
            "context": context_data,
            "model": "gpt-3.5-turbo"
        }
        
        response = client.post("/api/ai/chat", json=chat_data)
        
        # 如果AI客户端未初始化，会返回503
        assert response.status_code in [200, 503]


class TestWorkspaceAPI:
    """工作空间API测试"""
    
    def test_get_workspace_info(self, client):
        """测试获取工作空间信息"""
        response = client.get("/api/workspace/info")
        
        # 可能因为服务未初始化而返回503
        assert response.status_code in [200, 503]
        
        if response.status_code == 200:
            data = response.json()
            assert "root_path" in data
            assert "total_files" in data
            assert "total_directories" in data
            assert "total_size" in data
            assert "languages" in data
            assert "last_modified" in data
    
    def test_import_from_filesystem(self, client):
        """测试从文件系统导入"""
        import_data = {
            "real_path": "/tmp/test",
            "virtual_path": "/"
        }
        
        response = client.post("/api/workspace/import", json=import_data)
        
        # 可能因为服务未初始化或路径不存在而失败
        assert response.status_code in [200, 400, 503]


@pytest.mark.asyncio
class TestWebSocketAPI:
    """WebSocket API测试"""
    
    async def test_websocket_connection(self, async_client):
        """测试WebSocket连接"""
        try:
            async with async_client.websocket_connect("/ws/test-client") as websocket:
                # 发送ping消息
                await websocket.send_text(json.dumps({"type": "ping"}))
                
                # 接收pong响应
                response = await websocket.receive_text()
                data = json.loads(response)
                
                assert data["type"] == "pong"
        except Exception as e:
            # WebSocket可能因为各种原因连接失败，这在测试环境中是正常的
            pytest.skip(f"WebSocket connection failed: {e}")


class TestIntegrationWorkflow:
    """集成工作流测试"""
    
    def test_full_development_workflow(self, client):
        """测试完整的开发工作流"""
        # 1. 检查健康状态
        health_response = client.get("/health")
        assert health_response.status_code == 200
        
        # 2. 获取工作空间信息
        workspace_response = client.get("/api/workspace/info")
        # 可能返回503，这是正常的
        
        # 3. 尝试创建文件
        create_response = client.post("/api/files/create", json={
            "path": "/integration_test.py",
            "content": "# Integration test file\ndef test_function():\n    return 'test'",
            "encoding": "utf-8"
        })
        # 可能返回503，这是正常的
        
        # 4. 尝试获取文件树
        tree_response = client.get("/api/files/tree")
        # 可能返回503，这是正常的
        
        # 5. 尝试搜索
        search_response = client.post("/api/search/semantic", json={
            "query": "test function",
            "max_results": 5
        })
        # 可能返回503，这是正常的
        
        # 记录测试结果
        results = {
            "health": health_response.status_code,
            "workspace": workspace_response.status_code if 'workspace_response' in locals() else None,
            "create": create_response.status_code if 'create_response' in locals() else None,
            "tree": tree_response.status_code if 'tree_response' in locals() else None,
            "search": search_response.status_code if 'search_response' in locals() else None,
        }
        
        print(f"Integration test results: {results}")
        
        # 至少健康检查应该通过
        assert results["health"] == 200


class TestErrorHandling:
    """错误处理测试"""
    
    def test_invalid_json_request(self, client):
        """测试无效JSON请求"""
        response = client.post("/api/files/create", 
                             data="invalid json", 
                             headers={"Content-Type": "application/json"})
        assert response.status_code == 422  # Unprocessable Entity
    
    def test_missing_required_fields(self, client):
        """测试缺少必需字段"""
        response = client.post("/api/files/create", json={})
        assert response.status_code == 422  # Validation error
    
    def test_invalid_endpoints(self, client):
        """测试无效端点"""
        response = client.get("/api/nonexistent")
        assert response.status_code == 404
        
        response = client.post("/api/invalid/endpoint")
        assert response.status_code == 404


class TestPerformance:
    """性能测试"""
    
    def test_concurrent_requests(self, client):
        """测试并发请求"""
        import threading
        import time
        
        results = []
        
        def make_request():
            start_time = time.time()
            response = client.get("/health")
            end_time = time.time()
            results.append({
                "status_code": response.status_code,
                "response_time": end_time - start_time
            })
        
        # 创建10个并发请求
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # 等待所有请求完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(results) == 10
        assert all(r["status_code"] == 200 for r in results)
        
        # 检查平均响应时间（应该小于1秒）
        avg_response_time = sum(r["response_time"] for r in results) / len(results)
        assert avg_response_time < 1.0, f"Average response time too high: {avg_response_time}s"


if __name__ == '__main__':
    pytest.main([__file__, '-v', '-s']) 