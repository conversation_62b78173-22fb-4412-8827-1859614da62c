"""
VirtualFileSystem类测试用例

测试虚拟文件系统管理器的所有核心功能，包括：
- 路径操作和解析
- 基础文件系统操作（CRUD）
- 文件内容操作
- 目录遍历和搜索
- 持久化和导入导出
- 统计信息
"""

import pytest
import tempfile
import json
from pathlib import Path
from datetime import datetime

from app.vfs import VirtualFileSystem, FileType, FileNode


class TestVFSBasics:
    """测试VFS基础功能"""
    
    def test_vfs_initialization(self):
        """测试VFS初始化"""
        vfs = VirtualFileSystem()
        
        assert vfs.root.name == "/"
        assert vfs.root.is_directory
        assert vfs.current_path == "/"
        assert vfs.cwd == vfs.root
    
    def test_custom_root_name(self):
        """测试自定义根目录名称"""
        vfs = VirtualFileSystem("project")
        
        assert vfs.root.name == "project"
        assert vfs.current_path == "/project"


class TestPathOperations:
    """测试路径操作"""
    
    def setup_method(self):
        """测试前准备"""
        self.vfs = VirtualFileSystem()
    
    def test_normalize_path(self):
        """测试路径规范化"""
        # 绝对路径
        assert self.vfs.normalize_path("/home/<USER>") == "/home/<USER>"
        
        # 相对路径
        assert self.vfs.normalize_path("test") == "/test"
        assert self.vfs.normalize_path("./test") == "/test"
        
        # 复杂路径
        assert self.vfs.normalize_path("/home/<USER>/user/./file") == "/user/file"
    
    def test_resolve_path(self):
        """测试路径解析"""
        # 根路径
        assert self.vfs.resolve_path("/") == self.vfs.root
        
        # 不存在的路径
        assert self.vfs.resolve_path("/nonexistent") is None
        
        # 创建路径后再解析
        self.vfs.mkdir("/test")
        node = self.vfs.resolve_path("/test")
        assert node is not None
        assert node.name == "test"
    
    def test_path_existence_checks(self):
        """测试路径存在性检查"""
        # 根路径存在
        assert self.vfs.exists("/")
        assert self.vfs.is_directory("/")
        assert not self.vfs.is_file("/")
        
        # 不存在的路径
        assert not self.vfs.exists("/nonexistent")
        assert not self.vfs.is_file("/nonexistent")
        assert not self.vfs.is_directory("/nonexistent")


class TestDirectoryOperations:
    """测试目录操作"""
    
    def setup_method(self):
        """测试前准备"""
        self.vfs = VirtualFileSystem()
    
    def test_mkdir(self):
        """测试创建目录"""
        # 创建单层目录
        assert self.vfs.mkdir("/test")
        assert self.vfs.exists("/test")
        assert self.vfs.is_directory("/test")
        
        # 重复创建应该失败
        assert not self.vfs.mkdir("/test")
    
    def test_mkdir_parents(self):
        """测试递归创建目录"""
        # 不使用parents参数，应该失败
        assert not self.vfs.mkdir("/a/b/c")
        
        # 使用parents参数，应该成功
        assert self.vfs.mkdir("/a/b/c", parents=True)
        assert self.vfs.exists("/a")
        assert self.vfs.exists("/a/b")
        assert self.vfs.exists("/a/b/c")
    
    def test_list_directory(self):
        """测试列出目录内容"""
        # 创建测试结构
        self.vfs.mkdir("/test")
        self.vfs.touch("/test/file1.txt", "content1")
        self.vfs.touch("/test/file2.txt", "content2")
        self.vfs.mkdir("/test/subdir")
        self.vfs.touch("/test/.hidden", "hidden content")
        
        # 列出内容（不包含隐藏文件）
        contents = self.vfs.list_directory("/test")
        assert "file1.txt" in contents
        assert "file2.txt" in contents
        assert "subdir" in contents
        assert ".hidden" not in contents
        
        # 列出内容（包含隐藏文件）
        all_contents = self.vfs.list_directory("/test", include_hidden=True)
        assert len(all_contents) == 4
        assert ".hidden" in all_contents
    
    def test_walk_directory(self):
        """测试目录遍历"""
        # 创建测试结构
        self.vfs.mkdir("/project", parents=True)
        self.vfs.mkdir("/project/src")
        self.vfs.mkdir("/project/tests")
        self.vfs.touch("/project/README.md", "# Project")
        self.vfs.touch("/project/src/main.py", "print('hello')")
        self.vfs.touch("/project/tests/test_main.py", "def test(): pass")
        
        # 遍历目录
        walk_results = list(self.vfs.walk("/project"))
        
        # 验证结果
        assert len(walk_results) == 3  # project, src, tests
        
        # 检查根目录
        root_entry = walk_results[0]
        assert root_entry[0] == "/project"
        assert "src" in root_entry[1]
        assert "tests" in root_entry[1]
        assert "README.md" in root_entry[2]


class TestFileOperations:
    """测试文件操作"""
    
    def setup_method(self):
        """测试前准备"""
        self.vfs = VirtualFileSystem()
    
    def test_touch_file(self):
        """测试创建文件"""
        # 创建空文件
        assert self.vfs.touch("/test.txt")
        assert self.vfs.exists("/test.txt")
        assert self.vfs.is_file("/test.txt")
        
        # 创建带内容的文件
        assert self.vfs.touch("/content.txt", "Hello, World!")
        content = self.vfs.read_file("/content.txt")
        assert content == "Hello, World!"
    
    def test_write_and_read_file(self):
        """测试文件读写"""
        # 写入文件
        assert self.vfs.write_file("/test.txt", "Initial content")
        
        # 读取文件
        content = self.vfs.read_file("/test.txt")
        assert content == "Initial content"
        
        # 覆盖写入
        assert self.vfs.write_file("/test.txt", "Updated content")
        content = self.vfs.read_file("/test.txt")
        assert content == "Updated content"
    
    def test_append_file(self):
        """测试追加文件内容"""
        # 创建初始文件
        self.vfs.touch("/test.txt", "Line 1\n")
        
        # 追加内容
        assert self.vfs.append_file("/test.txt", "Line 2\n")
        assert self.vfs.append_file("/test.txt", "Line 3\n")
        
        # 验证内容
        content = self.vfs.read_file("/test.txt")
        assert content == "Line 1\nLine 2\nLine 3\n"
    
    def test_binary_content(self):
        """测试二进制内容处理"""
        binary_data = b'\x00\x01\x02\x03\xFF'
        
        # 写入二进制数据
        assert self.vfs.touch("/binary.bin", binary_data)
        
        # 读取二进制数据
        content = self.vfs.read_file("/binary.bin")
        assert content == binary_data
    
    def test_file_not_found_error(self):
        """测试文件不存在错误"""
        with pytest.raises(FileNotFoundError):
            self.vfs.read_file("/nonexistent.txt")
    
    def test_directory_error(self):
        """测试目录操作错误"""
        self.vfs.mkdir("/test_dir")
        
        with pytest.raises(IsADirectoryError):
            self.vfs.read_file("/test_dir")


class TestFileSystemOperations:
    """测试文件系统操作"""
    
    def setup_method(self):
        """测试前准备"""
        self.vfs = VirtualFileSystem()
    
    def test_remove_file(self):
        """测试删除文件"""
        # 创建并删除文件
        self.vfs.touch("/test.txt", "content")
        assert self.vfs.exists("/test.txt")
        
        assert self.vfs.remove("/test.txt")
        assert not self.vfs.exists("/test.txt")
    
    def test_remove_empty_directory(self):
        """测试删除空目录"""
        self.vfs.mkdir("/empty_dir")
        assert self.vfs.exists("/empty_dir")
        
        assert self.vfs.remove("/empty_dir")
        assert not self.vfs.exists("/empty_dir")
    
    def test_remove_non_empty_directory(self):
        """测试删除非空目录"""
        self.vfs.mkdir("/dir")
        self.vfs.touch("/dir/file.txt", "content")
        
        # 不使用recursive应该失败
        assert not self.vfs.remove("/dir")
        assert self.vfs.exists("/dir")
        
        # 使用recursive应该成功
        assert self.vfs.remove("/dir", recursive=True)
        assert not self.vfs.exists("/dir")
    
    def test_move_file(self):
        """测试移动文件"""
        self.vfs.touch("/source.txt", "content")
        
        assert self.vfs.move("/source.txt", "/destination.txt")
        assert not self.vfs.exists("/source.txt")
        assert self.vfs.exists("/destination.txt")
        assert self.vfs.read_file("/destination.txt") == "content"
    
    def test_move_directory(self):
        """测试移动目录"""
        self.vfs.mkdir("/source_dir")
        self.vfs.touch("/source_dir/file.txt", "content")
        
        assert self.vfs.move("/source_dir", "/dest_dir")
        assert not self.vfs.exists("/source_dir")
        assert self.vfs.exists("/dest_dir")
        assert self.vfs.exists("/dest_dir/file.txt")
    
    def test_copy_file(self):
        """测试复制文件"""
        self.vfs.touch("/source.txt", "original content")
        
        assert self.vfs.copy("/source.txt", "/copy.txt")
        assert self.vfs.exists("/source.txt")
        assert self.vfs.exists("/copy.txt")
        assert self.vfs.read_file("/copy.txt") == "original content"
        
        # 修改原文件不应影响副本
        self.vfs.write_file("/source.txt", "modified content")
        assert self.vfs.read_file("/copy.txt") == "original content"
    
    def test_copy_directory(self):
        """测试复制目录"""
        self.vfs.mkdir("/source_dir")
        self.vfs.touch("/source_dir/file1.txt", "content1")
        self.vfs.mkdir("/source_dir/subdir")
        self.vfs.touch("/source_dir/subdir/file2.txt", "content2")
        
        assert self.vfs.copy("/source_dir", "/copy_dir")
        
        # 验证复制结果
        assert self.vfs.exists("/copy_dir")
        assert self.vfs.exists("/copy_dir/file1.txt")
        assert self.vfs.exists("/copy_dir/subdir")
        assert self.vfs.exists("/copy_dir/subdir/file2.txt")
        assert self.vfs.read_file("/copy_dir/file1.txt") == "content1"
        assert self.vfs.read_file("/copy_dir/subdir/file2.txt") == "content2"


class TestSearchAndFind:
    """测试搜索和查找功能"""
    
    def setup_method(self):
        """测试前准备"""
        self.vfs = VirtualFileSystem()
        
        # 创建测试文件结构
        self.vfs.mkdir("/project", parents=True)
        self.vfs.touch("/project/main.py", "print('main')")
        self.vfs.touch("/project/config.json", "{}")
        self.vfs.mkdir("/project/src")
        self.vfs.touch("/project/src/utils.py", "def helper(): pass")
        self.vfs.touch("/project/src/test_utils.py", "def test(): pass")
        self.vfs.mkdir("/project/tests")
        self.vfs.touch("/project/tests/test_main.py", "def test_main(): pass")
    
    def test_find_by_pattern(self):
        """测试按模式查找"""
        # 查找所有Python文件
        py_files = self.vfs.find("*.py", "/project")
        assert len(py_files) == 4
        assert "/project/main.py" in py_files
        assert "/project/src/utils.py" in py_files
        
        # 查找所有测试文件
        test_files = self.vfs.find("test_*.py", "/project")
        assert len(test_files) == 2
        assert "/project/src/test_utils.py" in test_files
        assert "/project/tests/test_main.py" in test_files
    
    def test_find_by_file_type(self):
        """测试按文件类型查找"""
        # 只查找文件
        files = self.vfs.find("*", "/project", file_type=FileType.FILE)
        assert all(self.vfs.is_file(f) for f in files)
        
        # 只查找目录
        dirs = self.vfs.find("*", "/project", file_type=FileType.DIRECTORY)
        assert all(self.vfs.is_directory(d) for d in dirs)
        assert "/project/src" in dirs
        assert "/project/tests" in dirs


class TestPersistence:
    """测试持久化功能"""
    
    def setup_method(self):
        """测试前准备"""
        self.vfs = VirtualFileSystem()
        
        # 创建测试数据
        self.vfs.mkdir("/project")
        self.vfs.touch("/project/main.py", "print('hello')")
        self.vfs.touch("/project/README.md", "# Project")
        self.vfs.mkdir("/project/src")
        self.vfs.touch("/project/src/utils.py", "def helper(): pass")
    
    def test_save_and_load_json(self):
        """测试JSON格式的保存和加载"""
        with tempfile.TemporaryDirectory() as temp_dir:
            state_file = Path(temp_dir) / "vfs_state.json"
            
            # 保存状态
            assert self.vfs.save_state(str(state_file), "json")
            assert state_file.exists()
            
            # 创建新的VFS并加载状态
            new_vfs = VirtualFileSystem()
            assert new_vfs.load_state(str(state_file), "json")
            
            # 验证数据完整性
            assert new_vfs.exists("/project")
            assert new_vfs.exists("/project/main.py")
            assert new_vfs.read_file("/project/main.py") == "print('hello')"
            assert new_vfs.exists("/project/src/utils.py")
    
    def test_save_and_load_pickle(self):
        """测试Pickle格式的保存和加载"""
        with tempfile.TemporaryDirectory() as temp_dir:
            state_file = Path(temp_dir) / "vfs_state.pkl"
            
            # 保存状态
            assert self.vfs.save_state(str(state_file), "pickle")
            assert state_file.exists()
            
            # 创建新的VFS并加载状态
            new_vfs = VirtualFileSystem()
            assert new_vfs.load_state(str(state_file), "pickle")
            
            # 验证数据完整性
            assert new_vfs.exists("/project")
            assert new_vfs.read_file("/project/README.md") == "# Project"


class TestImportExport:
    """测试导入导出功能"""
    
    def setup_method(self):
        """测试前准备"""
        self.vfs = VirtualFileSystem()
    
    def test_export_to_filesystem(self):
        """测试导出到真实文件系统"""
        # 在VFS中创建测试数据
        self.vfs.mkdir("/export_test")
        self.vfs.touch("/export_test/file1.txt", "Content 1")
        self.vfs.touch("/export_test/file2.txt", "Content 2")
        self.vfs.mkdir("/export_test/subdir")
        self.vfs.touch("/export_test/subdir/file3.txt", "Content 3")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            export_path = Path(temp_dir) / "exported"
            
            # 导出到真实文件系统
            assert self.vfs.export_to_filesystem("/export_test", str(export_path))
            
            # 验证导出结果
            assert export_path.exists()
            assert (export_path / "file1.txt").exists()
            assert (export_path / "file1.txt").read_text() == "Content 1"
            assert (export_path / "subdir").exists()
            assert (export_path / "subdir" / "file3.txt").read_text() == "Content 3"
    
    def test_import_from_filesystem(self):
        """测试从真实文件系统导入"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件结构
            test_dir = Path(temp_dir) / "test_project"
            test_dir.mkdir()
            
            (test_dir / "main.py").write_text("print('imported')")
            (test_dir / "config.json").write_text('{"key": "value"}')
            
            subdir = test_dir / "src"
            subdir.mkdir()
            (subdir / "utils.py").write_text("def imported_func(): pass")
            
            # 导入到VFS
            assert self.vfs.import_from_filesystem(str(test_dir), "/imported")
            
            # 验证导入结果
            assert self.vfs.exists("/imported/test_project")
            assert self.vfs.exists("/imported/test_project/main.py")
            assert self.vfs.read_file("/imported/test_project/main.py") == "print('imported')"
            assert self.vfs.exists("/imported/test_project/src/utils.py")


class TestStatistics:
    """测试统计信息功能"""
    
    def setup_method(self):
        """测试前准备"""
        self.vfs = VirtualFileSystem()
        
        # 创建测试数据
        self.vfs.mkdir("/project")
        self.vfs.touch("/project/main.py", "print('hello')")  # Python文件
        self.vfs.touch("/project/config.json", "{}")         # JSON文件
        self.vfs.mkdir("/project/src")
        self.vfs.touch("/project/src/utils.py", "def helper(): pass")  # 另一个Python文件
        self.vfs.mkdir("/project/tests")
        self.vfs.touch("/project/tests/test.py", "def test(): pass")   # 测试文件
    
    def test_get_stats(self):
        """测试获取统计信息"""
        stats = self.vfs.get_stats()
        
        # 验证基本统计信息
        assert stats["total_files"] == 4  # 4个文件
        assert stats["total_directories"] == 4  # root + project + src + tests
        assert stats["total_size"] > 0
        assert stats["max_depth"] >= 2  # 至少2层深度
        
        # 验证文件类型统计
        assert "python" in stats["file_types"]
        assert stats["file_types"]["python"] == 3  # 3个Python文件
        assert "json" in stats["file_types"]
        assert stats["file_types"]["json"] == 1  # 1个JSON文件


class TestConcurrency:
    """测试并发安全"""
    
    def setup_method(self):
        """测试前准备"""
        self.vfs = VirtualFileSystem()
    
    def test_concurrent_operations(self):
        """测试并发操作"""
        import threading
        import time
        
        errors = []
        
        def create_files(start_idx):
            try:
                for i in range(start_idx, start_idx + 10):
                    self.vfs.mkdir(f"/dir_{i}")
                    self.vfs.touch(f"/dir_{i}/file_{i}.txt", f"content_{i}")
                    time.sleep(0.001)  # 小延迟增加竞争条件
            except Exception as e:
                errors.append(e)
        
        def read_files():
            try:
                for i in range(50):
                    if self.vfs.exists(f"/dir_{i}"):
                        files = self.vfs.list_directory(f"/dir_{i}")
                        if files:
                            content = self.vfs.read_file(f"/dir_{i}/{files[0]}")
                    time.sleep(0.001)
            except Exception as e:
                errors.append(e)
        
        # 启动多个线程
        threads = []
        for i in range(0, 50, 10):
            thread = threading.Thread(target=create_files, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 启动读取线程
        read_thread = threading.Thread(target=read_files)
        threads.append(read_thread)
        read_thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证没有发生错误
        assert len(errors) == 0, f"Concurrent operations failed: {errors}"
        
        # 验证最终状态
        stats = self.vfs.get_stats()
        assert stats["total_files"] >= 5  # 至少创建了一些文件
        assert stats["total_directories"] >= 5  # 至少创建了一些目录


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 