# 🤖 AI Agent IDE - 现代化AI编程工具

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![React 18](https://img.shields.io/badge/react-18.0+-61dafb.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/typescript-5.0+-3178c6.svg)](https://www.typescriptlang.org/)

> 一个基于Web的AI编程助手，提供现代化的VSCode风格界面和强大的AI能力

## ✨ 核心特性

### 🎨 现代化界面设计
- **Apple风格的简洁设计** - 参考Windsurf和Cursor的最佳实践
- **专业级编程体验** - Monaco Editor + VSCode主题
- **响应式布局** - 可调整的面板大小和布局
- **丰富的快捷键** - 完整的键盘快捷键系统

### 🚀 核心功能模块

#### 📁 智能文件管理器
- 树形文件浏览和操作
- 右键上下文菜单
- 文件类型识别和图标
- 实时状态指示

#### 💻 专业代码编辑器  
- 基于Monaco Editor的强大编辑器
- 多语言语法高亮
- 智能代码补全
- 搜索和替换功能
- 实时错误检测

#### 🤖 AI编程助手
- 现代化聊天界面
- 快速操作按钮（解释代码、编写测试、优化等）
- 上下文感知的AI对话
- 代码生成和重构建议

#### ⚡ 命令面板
- VSCode风格的命令搜索
- 分类命令组织
- 键盘导航支持
- 智能筛选和匹配

## 🛠️ 技术栈

### 前端
- **React 18** + TypeScript
- **Vite** 构建工具
- **TailwindCSS** 样式框架
- **Monaco Editor** 代码编辑器
- **Lucide React** 图标库

### 后端
- **FastAPI** Web框架
- **Python 3.11+**
- **虚拟文件系统** (VFS)
- **上下文引擎** (Context Engine)
- **语义搜索** (Semantic Search)

## 🚀 快速开始

### 环境要求
- Node.js 18+
- Python 3.11+
- npm 或 yarn

### 安装和运行

1. **克隆项目**
```bash
git clone <repository-url>
cd ai-agent-ide
```

2. **安装前端依赖**
```bash
cd frontend
npm install
```

3. **安装后端依赖**
```bash
pip install -r requirements.txt
```

4. **启动开发服务器**

后端服务：
```bash
python -m uvicorn app.api_service:app --reload --host 0.0.0.0 --port 8000
```

前端服务：
```bash
cd frontend
npm run dev
```

5. **访问应用**
- 前端界面: http://localhost:5173
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## ⌨️ 快捷键

| 快捷键 | 功能 |
|--------|------|
| `⌘K` / `Ctrl+K` | 打开命令面板 |
| `⌘B` / `Ctrl+B` | 切换聊天面板 |
| `⌘,` / `Ctrl+,` | 打开设置 |
| `⌘W` / `Ctrl+W` | 关闭当前标签 |
| `⌘S` / `Ctrl+S` | 保存文件 |
| `⌘F` / `Ctrl+F` | 搜索 |

## 📁 项目结构

```
ai-agent-ide/
├── frontend/              # 前端React应用
│   ├── src/
│   │   ├── components/    # React组件
│   │   │   ├── FileExplorer.tsx
│   │   │   ├── CodeEditor.tsx
│   │   │   ├── ChatPanel.tsx
│   │   │   ├── CommandPalette.tsx
│   │   │   ├── StatusBar.tsx
│   │   │   └── SettingsPanel.tsx
│   │   ├── App.tsx        # 主应用组件
│   │   └── index.css      # 全局样式
│   └── package.json
├── app/                   # 后端Python应用
│   ├── vfs.py            # 虚拟文件系统
│   ├── context_engine.py # 上下文引擎
│   ├── api_service.py    # API服务
│   └── __init__.py
├── tests/                # 测试文件
├── docs/                 # 文档
├── scripts/              # 工具脚本
├── requirements.txt      # Python依赖
└── README.md
```

## 🎯 主要功能

### 文件管理
- ✅ 虚拟文件系统
- ✅ 文件树浏览
- ✅ 文件CRUD操作
- ✅ 文件搜索和过滤

### 代码编辑
- ✅ 语法高亮
- ✅ 代码补全
- ✅ 错误检测
- ✅ 搜索替换
- ✅ 多标签编辑

### AI助手
- ✅ 上下文感知对话
- ✅ 代码解释和优化
- ✅ 自动测试生成
- ✅ 智能重构建议

### 用户界面
- ✅ 现代化设计
- ✅ 响应式布局
- ✅ 自定义主题
- ✅ 快捷键支持

## 🔧 开发

### 代码规范
- TypeScript严格模式
- ESLint代码检查
- Prettier代码格式化
- 组件化开发

### 构建部署
```bash
# 前端构建
cd frontend
npm run build

# 类型检查
npm run type-check

# 代码检查
npm run lint
```

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**享受现代化的AI编程体验！** 🚀

## 📋 开发路线图

### v1.0 (当前版本)
- ✅ 基础AI编程功能
- ✅ Web界面和API
- ✅ 虚拟文件系统
- ✅ 代码diff和应用

### v1.1 (计划中)
- 🔄 Monaco编辑器集成
- 🔄 多种AI模型支持
- 🔄 插件系统
- 🔄 协作功能

### v2.0 (未来)
- 📋 VSCode插件版本
- 📋 云端部署版本
- 📋 企业级功能
- 📋 移动端支持

## 🐛 问题反馈

遇到问题？请通过以下方式反馈：

- [GitHub Issues](https://github.com/your-repo/issues)
- [讨论区](https://github.com/your-repo/discussions)
- 邮箱: <EMAIL>

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- **Cursor**: 灵感来源和功能参考
- **VSCode**: UI设计和交互模式
- **FastAPI**: 现代化的Python Web框架
- **React**: 强大的前端框架
- **所有贡献者**: 感谢每一位贡献者的努力

---

<div align="center">

**⭐ 如果这个项目对你有帮助，请给我们一个星标！**

Made with ❤️ by AI Programming Tool Team

</div> 