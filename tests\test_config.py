"""
环境验证测试

验证Python环境是否正确配置，关键依赖是否可以导入
"""

import sys
import pytest
from pathlib import Path


class TestEnvironment:
    """环境配置测试"""

    def test_python_version(self):
        """测试Python版本"""
        assert sys.version_info >= (3, 8), f"Python版本过低: {sys.version}"

    def test_project_structure(self):
        """测试项目结构"""
        root_dir = Path(__file__).parent.parent
        
        # 检查必要的目录
        required_dirs = [
            "app",
            "frontend", 
            "tests",
            "docs",
            "scripts",
            "data"
        ]
        
        for dir_name in required_dirs:
            dir_path = root_dir / dir_name
            assert dir_path.exists(), f"缺少目录: {dir_name}"
            assert dir_path.is_dir(), f"{dir_name} 不是目录"

    def test_required_files(self):
        """测试必要的配置文件"""
        root_dir = Path(__file__).parent.parent
        
        required_files = [
            "requirements.txt",
            "README.md",
            "pytest.ini",
            ".gitignore",
            "docker-compose.yml"
        ]
        
        for file_name in required_files:
            file_path = root_dir / file_name
            assert file_path.exists(), f"缺少文件: {file_name}"
            assert file_path.is_file(), f"{file_name} 不是文件"


class TestDependencyImports:
    """依赖导入测试"""

    def test_fastapi_import(self):
        """测试FastAPI导入"""
        try:
            import fastapi
            assert hasattr(fastapi, 'FastAPI')
        except ImportError as e:
            pytest.fail(f"FastAPI导入失败: {e}")

    def test_uvicorn_import(self):
        """测试Uvicorn导入"""
        try:
            import uvicorn
            assert hasattr(uvicorn, 'run')
        except ImportError as e:
            pytest.fail(f"Uvicorn导入失败: {e}")

    def test_pydantic_import(self):
        """测试Pydantic导入"""
        try:
            import pydantic
            assert hasattr(pydantic, 'BaseModel')
        except ImportError as e:
            pytest.fail(f"Pydantic导入失败: {e}")

    @pytest.mark.slow
    def test_openai_import(self):
        """测试OpenAI导入"""
        try:
            import openai
            assert hasattr(openai, 'OpenAI')
        except ImportError as e:
            pytest.fail(f"OpenAI导入失败: {e}")

    @pytest.mark.slow
    def test_tree_sitter_import(self):
        """测试Tree-sitter导入"""
        try:
            import tree_sitter
            assert hasattr(tree_sitter, 'Language')
        except ImportError as e:
            pytest.skip(f"Tree-sitter导入失败（可选）: {e}")

    @pytest.mark.slow
    def test_sentence_transformers_import(self):
        """测试Sentence Transformers导入"""
        try:
            import sentence_transformers
            assert hasattr(sentence_transformers, 'SentenceTransformer')
        except ImportError as e:
            pytest.skip(f"Sentence Transformers导入失败（可选）: {e}")

    @pytest.mark.slow
    def test_faiss_import(self):
        """测试FAISS导入"""
        try:
            import faiss
            assert hasattr(faiss, 'IndexFlatL2')
        except ImportError as e:
            pytest.skip(f"FAISS导入失败（可选）: {e}")


class TestConfiguration:
    """配置测试"""

    def test_environment_variables(self):
        """测试环境变量配置"""
        import os
        
        # 测试环境下的环境变量
        assert os.getenv("TESTING") == "true"
        assert os.getenv("DEBUG") == "true"
        assert os.getenv("OPENAI_API_KEY") is not None

    def test_app_initialization(self):
        """测试应用初始化"""
        try:
            # 这个测试只检查app包是否可以导入
            # 具体的应用初始化在其他测试中进行
            import app
            assert hasattr(app, '__version__')
        except ImportError as e:
            pytest.fail(f"App包导入失败: {e}")


class TestFileOperations:
    """文件操作测试"""

    def test_data_directory_access(self):
        """测试数据目录访问权限"""
        data_dir = Path(__file__).parent.parent / "data"
        assert data_dir.exists()
        
        # 测试读写权限
        test_file = data_dir / "test_write.txt"
        try:
            test_file.write_text("test")
            content = test_file.read_text()
            assert content == "test"
        finally:
            if test_file.exists():
                test_file.unlink()

    def test_temp_directory_creation(self):
        """测试临时目录创建"""
        import tempfile
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            assert temp_path.exists()
            assert temp_path.is_dir()
            
            # 测试在临时目录中创建文件
            test_file = temp_path / "test.txt"
            test_file.write_text("temporary test")
            assert test_file.read_text() == "temporary test"


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 