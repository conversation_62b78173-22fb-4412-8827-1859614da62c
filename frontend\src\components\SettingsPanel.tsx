import React, { useState } from 'react';
import {
  X,
  Settings,
  Palette,
  Keyboard,
  Zap,
  Code,
  Terminal,
  Eye,
  Volume2,
  Globe,
  Shield,
  HardDrive,
  Monitor,
  User,
  Bell
} from 'lucide-react';

interface SettingsPanelProps {
  onClose: () => void;
}

interface SettingSection {
  id: string;
  title: string;
  icon: React.ElementType;
  description: string;
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({ onClose }) => {
  const [activeSection, setActiveSection] = useState('general');
  const [settings, setSettings] = useState({
    // General
    theme: 'dark',
    fontSize: 14,
    fontFamily: 'Monaco',
    tabSize: 2,
    wordWrap: true,
    autoSave: true,
    minimap: true,
    lineNumbers: true,
    
    // AI
    aiModel: 'gpt-3.5-turbo',
    aiApiKey: '',
    enableAutoComplete: true,
    enableInlineChat: true,
    maxTokens: 2000,
    
    // Editor
    bracketPairColorization: true,
    renderWhitespace: 'selection',
    cursorBlinking: 'smooth',
    smoothScrolling: true,
    mouseWheelZoom: true,
    
    // Terminal
    terminalFontSize: 12,
    terminalShell: '/bin/bash',
    
    // Notifications
    enableNotifications: true,
    soundEnabled: false,
    
    // Privacy
    telemetry: false,
    crashReporting: true
  });

  const sections: SettingSection[] = [
    {
      id: 'general',
      title: 'General', 
      icon: Settings,
      description: 'Basic application settings'
    },
    {
      id: 'appearance',
      title: 'Appearance',
      icon: Palette,
      description: 'Theme and visual customization'
    },
    {
      id: 'editor',
      title: 'Editor',
      icon: Code,
      description: 'Code editor preferences'
    },
    {
      id: 'ai',
      title: 'AI Assistant',
      icon: Zap,
      description: 'AI model and behavior settings'
    },
    {
      id: 'terminal',
      title: 'Terminal',
      icon: Terminal,
      description: 'Terminal configuration'
    },
    {
      id: 'shortcuts',
      title: 'Keyboard',
      icon: Keyboard,
      description: 'Keyboard shortcuts'
    },
    {
      id: 'privacy',
      title: 'Privacy',
      icon: Shield,
      description: 'Privacy and data settings'
    }
  ];

  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">General Settings</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Auto Save</label>
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={settings.autoSave}
              onChange={(e) => updateSetting('autoSave', e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-[var(--text-secondary)]">
              Automatically save files when editing
            </span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Language</label>
          <select 
            value="en" 
            className="input w-40"
          >
            <option value="en">English</option>
            <option value="zh">中文</option>
            <option value="es">Español</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Appearance</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Theme</label>
          <div className="grid grid-cols-2 gap-2">
            {['dark', 'light'].map(theme => (
              <button
                key={theme}
                onClick={() => updateSetting('theme', theme)}
                className={`p-3 border rounded-lg text-left ${
                  settings.theme === theme 
                    ? 'border-[var(--accent-blue)] bg-[var(--accent-blue)]/10' 
                    : 'border-[var(--border-primary)]'
                }`}
              >
                <div className="font-medium capitalize">{theme}</div>
                <div className="text-xs text-[var(--text-secondary)]">
                  {theme === 'dark' ? 'Dark theme' : 'Light theme'}
                </div>
              </button>
            ))}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Font Size</label>
          <input
            type="range"
            min="10"
            max="20"
            value={settings.fontSize}
            onChange={(e) => updateSetting('fontSize', parseInt(e.target.value))}
            className="w-full"
          />
          <div className="text-sm text-[var(--text-secondary)] mt-1">
            {settings.fontSize}px
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Font Family</label>
          <select 
            value={settings.fontFamily}
            onChange={(e) => updateSetting('fontFamily', e.target.value)}
            className="input w-full"
          >
            <option value="Monaco">Monaco</option>
            <option value="Cascadia Code">Cascadia Code</option>
            <option value="Consolas">Consolas</option>
            <option value="Fira Code">Fira Code</option>
            <option value="JetBrains Mono">JetBrains Mono</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderEditorSettings = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Editor</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Tab Size</label>
          <input
            type="number"
            min="1"
            max="8"
            value={settings.tabSize}
            onChange={(e) => updateSetting('tabSize', parseInt(e.target.value))}
            className="input w-20"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Word Wrap</label>
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={settings.wordWrap}
              onChange={(e) => updateSetting('wordWrap', e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-[var(--text-secondary)]">
              Wrap long lines
            </span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Minimap</label>
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={settings.minimap}
              onChange={(e) => updateSetting('minimap', e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-[var(--text-secondary)]">
              Show code minimap
            </span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Line Numbers</label>
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={settings.lineNumbers}
              onChange={(e) => updateSetting('lineNumbers', e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-[var(--text-secondary)]">
              Show line numbers
            </span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Bracket Pair Colorization</label>
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={settings.bracketPairColorization}
              onChange={(e) => updateSetting('bracketPairColorization', e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-[var(--text-secondary)]">
              Colorize matching brackets
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAISettings = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">AI Assistant</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">AI Model</label>
          <select 
            value={settings.aiModel}
            onChange={(e) => updateSetting('aiModel', e.target.value)}
            className="input w-full"
          >
            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
            <option value="gpt-4">GPT-4</option>
            <option value="claude-3-sonnet">Claude 3 Sonnet</option>
            <option value="claude-3-haiku">Claude 3 Haiku</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">API Key</label>
          <input
            type="password"
            value={settings.aiApiKey}
            onChange={(e) => updateSetting('aiApiKey', e.target.value)}
            placeholder="Enter your API key..."
            className="input w-full"
          />
          <div className="text-xs text-[var(--text-tertiary)] mt-1">
            Your API key is stored locally and never shared
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Max Tokens</label>
          <input
            type="number"
            min="100"
            max="4000"
            value={settings.maxTokens}
            onChange={(e) => updateSetting('maxTokens', parseInt(e.target.value))}
            className="input w-32"
          />
          <div className="text-xs text-[var(--text-tertiary)] mt-1">
            Maximum tokens per AI response
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Auto Complete</label>
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={settings.enableAutoComplete}
              onChange={(e) => updateSetting('enableAutoComplete', e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-[var(--text-secondary)]">
              Enable AI-powered auto completion
            </span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Inline Chat</label>
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={settings.enableInlineChat}
              onChange={(e) => updateSetting('enableInlineChat', e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-[var(--text-secondary)]">
              Enable inline AI chat in editor
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSection = () => {
    switch (activeSection) {
      case 'general':
        return renderGeneralSettings();
      case 'appearance':
        return renderAppearanceSettings();
      case 'editor':
        return renderEditorSettings();
      case 'ai':
        return renderAISettings();
      default:
        return (
          <div className="flex items-center justify-center h-64 text-[var(--text-tertiary)]">
            <div className="text-center">
              <Settings className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>Settings section coming soon</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg shadow-2xl w-full max-w-4xl h-[80vh] flex">
        {/* Sidebar */}
        <div className="w-64 border-r border-[var(--border-primary)] flex flex-col">
          <div className="p-4 border-b border-[var(--border-primary)] flex items-center justify-between">
            <h2 className="text-lg font-semibold">Settings</h2>
            <button
              onClick={onClose}
              className="btn-ghost p-1"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          
          <div className="flex-1 overflow-auto">
            {sections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`w-full p-3 text-left flex items-center gap-3 hover:bg-[var(--bg-tertiary)] ${
                  activeSection === section.id ? 'bg-[var(--bg-quaternary)] border-r-2 border-[var(--accent-blue)]' : ''
                }`}
              >
                <section.icon className="w-4 h-4 text-[var(--text-secondary)]" />
                <div>
                  <div className="text-sm font-medium">{section.title}</div>
                  <div className="text-xs text-[var(--text-tertiary)]">{section.description}</div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col">
          <div className="flex-1 overflow-auto p-6">
            {renderSection()}
          </div>
          
          <div className="p-4 border-t border-[var(--border-primary)] flex items-center justify-between">
            <div className="text-xs text-[var(--text-tertiary)]">
              Settings are automatically saved
            </div>
            <div className="flex gap-2">
              <button className="btn-ghost px-4 py-2">
                Reset to Defaults
              </button>
              <button 
                onClick={onClose}
                className="btn-primary px-4 py-2"
              >
                Done
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPanel; 