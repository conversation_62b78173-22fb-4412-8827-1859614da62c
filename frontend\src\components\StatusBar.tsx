import React from 'react';
import { 
  Wif<PERSON>, 
  Wifi<PERSON>ff, 
  FileText, 
  GitBranch, 
  AlertCircle, 
  CheckCircle,
  Clock,
  Setting<PERSON>,
  Zap
} from 'lucide-react';

interface FileTab {
  id: string;
  name: string;
  path: string;
  content: string;
  language: string;
  modified: boolean;
}

interface StatusBarProps {
  isConnected: boolean;
  activeTab?: FileTab;
  totalTabs: number;
}

const StatusBar: React.FC<StatusBarProps> = ({ isConnected, activeTab, totalTabs }) => {
  const [currentTime, setCurrentTime] = React.useState(new Date());

  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const getLanguageDisplayName = (language: string): string => {
    const languageMap: { [key: string]: string } = {
      'javascript': 'JavaScript',
      'typescript': 'TypeScript',
      'python': 'Python',
      'json': 'JSON',
      'html': 'HTML',
      'css': 'CSS',
      'scss': 'SCSS',
      'markdown': 'Markdown',
      'yaml': 'YAML',
      'plaintext': 'Text'
    };
    
    return languageMap[language] || language.toUpperCase();
  };

  const getFileSize = (content: string): string => {
    const bytes = new Blob([content]).size;
    if (bytes < 1024) return `${bytes}B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
  };

  const getLineCount = (content: string): number => {
    return content.split('\n').length;
  };

  return (
    <div className="status-bar">
      {/* Left side */}
      <div className="flex items-center gap-4">
        {/* Connection Status */}
        <div className="flex items-center gap-1">
          {isConnected ? (
            <>
              <Wifi className="w-3 h-3 text-[var(--accent-green)]" />
              <span className="text-[var(--accent-green)]">Connected</span>
            </>
          ) : (
            <>
              <WifiOff className="w-3 h-3 text-[var(--accent-red)]" />
              <span className="text-[var(--accent-red)]">Disconnected</span>
            </>
          )}
        </div>

        {/* Git Branch */}
        <div className="flex items-center gap-1">
          <GitBranch className="w-3 h-3" />
          <span>main</span>
        </div>

        {/* Active File Info */}
        {activeTab && (
          <>
            <div className="w-px h-4 bg-[var(--border-primary)]" />
            
            <div className="flex items-center gap-1">
              <FileText className="w-3 h-3" />
              <span>{activeTab.name}</span>
              
              {activeTab.modified && (
                <div className="w-1.5 h-1.5 rounded-full bg-[var(--accent-orange)]" />
              )}
            </div>

            <span>{getLanguageDisplayName(activeTab.language)}</span>
            
            <span>{getFileSize(activeTab.content)}</span>
            
            <span>{getLineCount(activeTab.content)} lines</span>
          </>
        )}

        {/* Error/Warning Indicators */}
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <AlertCircle className="w-3 h-3 text-[var(--accent-red)]" />
            <span>0</span>
          </div>
          
          <div className="flex items-center gap-1">
            <AlertCircle className="w-3 h-3 text-[var(--accent-orange)]" />
            <span>0</span>
          </div>
        </div>
      </div>

      {/* Right side */}
      <div className="flex items-center gap-4">
        {/* Tab Count */}
        {totalTabs > 0 && (
          <span>{totalTabs} tab{totalTabs !== 1 ? 's' : ''}</span>
        )}

        {/* AI Status */}
        <div className="flex items-center gap-1">
          <Zap className="w-3 h-3 text-[var(--accent-blue)]" />
          <span>AI Ready</span>
        </div>

        {/* Encoding */}
        <span>UTF-8</span>

        {/* Line Ending */}
        <span>LF</span>

        {/* Current Time */}
        <div className="flex items-center gap-1">
          <Clock className="w-3 h-3" />
          <span>{currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
        </div>

        {/* Settings */}
        <button className="flex items-center gap-1 hover:bg-[var(--bg-tertiary)] px-1 py-0.5 rounded">
          <Settings className="w-3 h-3" />
        </button>
      </div>
    </div>
  );
};

export default StatusBar; 