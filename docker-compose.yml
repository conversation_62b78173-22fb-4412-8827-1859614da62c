version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DEBUG=true
      - HOST=0.0.0.0
      - PORT=8000
    volumes:
      - ./app:/app/app
      - ./data:/app/data
      - ./tests:/app/tests
    depends_on:
      - redis
    networks:
      - ai-tool-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端开发服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - VITE_API_URL=http://localhost:8000
      - VITE_WS_URL=ws://localhost:8000
    depends_on:
      - backend
    networks:
      - ai-tool-network
    restart: unless-stopped

  # Redis缓存服务（用于会话管理和向量缓存）
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-tool-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx反向代理（生产环境）
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./frontend/dist:/usr/share/nginx/html
    depends_on:
      - backend
      - frontend
    networks:
      - ai-tool-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  redis_data:

networks:
  ai-tool-network:
    driver: bridge 