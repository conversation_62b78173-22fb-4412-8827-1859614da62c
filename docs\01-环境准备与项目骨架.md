# 阶段一：环境准备与项目骨架

## 🎯 任务目标

建立AI编程工具项目的完整开发环境和基础架构，为后续功能开发奠定坚实基础。

## 📋 任务拆解

### 子任务1: 项目目录结构创建
**目标**: 创建标准化的项目目录结构

**LLM提示词**:
```
你是一名资深的全栈开发工程师。我需要创建一个AI编程工具项目的目录结构。

请提供Shell命令来创建以下目录结构：
- ai-programming-tool/ (项目根目录)
  - app/ (后端Python代码)
  - frontend/ (前端React代码)
  - tests/ (测试文件)
  - docs/ (项目文档)
  - scripts/ (构建和部署脚本)
  - data/ (数据文件目录)

要求:
1. 使用标准的Shell命令创建目录结构
2. 在每个目录下创建.gitkeep文件以确保目录被Git跟踪
3. 提供Windows和Linux/macOS两套命令

请只提供可执行的Shell命令，不需要额外解释。
```

**测试验证方式**:
- 执行`ls -la`或`dir`命令确认所有目录已创建
- 检查每个目录下是否存在.gitkeep文件
- 验证目录权限是否正确设置

### 子任务2: Python后端环境准备
**目标**: 配置Python虚拟环境和基础依赖

**LLM提示词**:
```
你是Python后端开发专家。现在需要为AI编程工具项目配置Python后端环境。

请提供以下配置文件和命令：

1. 创建requirements.txt文件，包含以下依赖：
   - FastAPI (最新稳定版)
   - Uvicorn (ASGI服务器)
   - Pydantic (数据验证)
   - OpenAI (AI接口)
   - python-dotenv (环境变量)
   - tree-sitter (代码解析)
   - sentence-transformers (语义搜索)
   - faiss-cpu (向量搜索)
   - patch-ng (diff处理)
   - pytest及相关测试库

2. 创建.env.example文件，包含必要的环境变量模板

3. 创建app/__init__.py文件标识Python包

4. 提供创建虚拟环境和安装依赖的命令

请确保版本兼容性，并提供Windows和Linux/macOS的命令。
```

**测试验证方式**:
- 创建并激活虚拟环境: `python -m venv venv`
- 安装依赖: `pip install -r requirements.txt`
- 验证关键库导入: `python -c "import fastapi, openai, tree_sitter"`
- 检查虚拟环境: `pip list`确认所有依赖已安装

### 子任务3: 前端React项目初始化
**目标**: 初始化React + TypeScript前端项目

**LLM提示词**:
```
你是前端开发专家，精通React和TypeScript。需要初始化一个现代化的React前端项目。

在frontend/目录下创建React + TypeScript项目，要求：

1. 使用Vite作为构建工具
2. 集成TypeScript
3. 配置Tailwind CSS
4. 安装必要的开发依赖：
   - React 18+
   - TypeScript
   - Vite
   - Tailwind CSS
   - @types/react, @types/react-dom
   - ESLint, Prettier

5. 创建基础的项目配置文件：
   - tsconfig.json
   - tailwind.config.js
   - vite.config.ts
   - .eslintrc.json

6. 设置package.json的scripts脚本

请提供完整的命令序列和配置文件内容。只需要配置文件内容，不需要React组件代码。
```

**测试验证方式**:
- 进入frontend目录: `cd frontend`
- 安装依赖: `npm install`
- 启动开发服务器: `npm run dev`
- 访问http://localhost:3000验证页面加载
- 验证TypeScript编译: `npm run build`
- 检查依赖: `npm list`确认所有包已安装

### 子任务4: 基础配置文件创建
**目标**: 创建项目的基础配置和文档文件

**LLM提示词**:
```
你是DevOps工程师和技术文档专家。需要为AI编程工具项目创建基础配置文件。

请创建以下文件：

1. .gitignore文件 - 包含Python和Node.js项目的标准忽略规则
2. README.md文件 - 项目说明，包含功能介绍、安装指南、使用方法
3. LICENSE文件 - MIT许可证
4. .env.example文件 - 环境变量模板
5. docker-compose.yml文件 - 基础的开发环境配置
6. Dockerfile文件 - Python后端的容器配置

要求：
- 文件内容要完整且专业
- README.md要包含项目结构说明
- docker-compose.yml要包含后端、前端、数据库服务
- 环境变量要考虑安全性

请为每个文件提供完整内容，不需要代码实现细节。
```

**测试验证方式**:
- 检查文件存在性: `ls -la`确认所有配置文件已创建
- 验证.gitignore: `git status`检查忽略规则是否生效
- 测试Docker配置: `docker-compose config`验证YAML语法
- 检查环境变量: 复制.env.example为.env并填入测试值
- 验证文档: 阅读README.md确保信息完整准确

### 子任务5: 基础测试框架搭建
**目标**: 建立项目的测试基础设施

**LLM提示词**:
```
你是测试工程师。需要为AI编程工具项目建立测试基础设施。

请提供：

1. 在tests/目录下创建测试结构：
   - tests/__init__.py
   - tests/test_config.py (测试配置验证)
   - tests/conftest.py (pytest配置)

2. 创建pytest.ini配置文件，包含：
   - 测试发现规则
   - 输出格式配置
   - 覆盖率配置
   - 异步测试配置

3. 创建一个简单的环境验证测试用例，验证：
   - Python环境是否正确
   - 关键依赖是否可导入
   - 环境变量配置是否正确

4. 创建前端测试配置（如果使用Jest）

请只提供测试框架配置，不需要具体的功能测试代码。
```

**测试验证方式**:
- 运行基础测试: `pytest tests/test_config.py -v`
- 检查测试发现: `pytest --collect-only`
- 验证覆盖率: `pytest --cov=app tests/`
- 测试前端构建: `cd frontend && npm test`（如果配置了测试）
- 检查测试配置: 确认pytest.ini和conftest.py配置正确

## 🧪 阶段验收标准

完成本阶段后，项目应满足以下条件：

### 环境验证
1. ✅ Python虚拟环境创建成功并可激活
2. ✅ 所有Python依赖安装无错误
3. ✅ Node.js和npm环境正常工作
4. ✅ 前端项目可成功启动开发服务器

### 结构验证
1. ✅ 项目目录结构完整创建
2. ✅ 所有配置文件存在且语法正确
3. ✅ Git仓库初始化并配置正确的忽略规则
4. ✅ 基础测试框架可正常运行

### 功能验证
1. ✅ 后端基础服务可启动（即使是空服务）
2. ✅ 前端开发服务可启动并访问
3. ✅ Docker配置文件语法验证通过
4. ✅ 测试命令可正常执行

### 文档验证
1. ✅ README.md信息完整准确
2. ✅ 环境变量配置说明清晰
3. ✅ 项目结构说明准确
4. ✅ 安装和启动指南可操作

## 🚨 常见问题解决

### Python环境问题
- **问题**: 虚拟环境创建失败
- **测试方法**: 尝试`python -m venv --help`检查venv模块是否可用
- **解决思路**: 升级Python版本或使用conda环境

### Node.js环境问题  
- **问题**: npm install失败
- **测试方法**: 运行`npm doctor`检查npm环境
- **解决思路**: 清理npm缓存或使用yarn替代

### 依赖冲突问题
- **问题**: Python包版本冲突
- **测试方法**: 使用`pip check`检查依赖兼容性
- **解决思路**: 调整requirements.txt中的版本约束

## 📝 完成检查清单

- [ ] 项目目录结构创建完成
- [ ] Python虚拟环境配置成功
- [ ] 后端依赖安装无错误
- [ ] 前端React项目初始化完成
- [ ] 所有配置文件创建并验证
- [ ] Git仓库初始化完成
- [ ] 基础测试框架搭建完成
- [ ] README和文档完整准确
- [ ] 环境验证测试全部通过

---

**🎯 阶段目标**: 建立完整的开发环境和项目基础架构，为后续功能开发做好准备。 