import React, { useState, useRef, useEffect } from 'react';
import {
  Send,
  Bot,
  User,
  Co<PERSON>,
  RotateCcw,
  Trash2,
  Plus,
  Settings,
  Zap,
  Code,
  FileText,
  MessageSquare,
  ArrowUp,
  Sparkles
} from 'lucide-react';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface ChatPanelProps {
  messages: ChatMessage[];
  onSendMessage: (message: string) => Promise<void>;
  isLoading: boolean;
}

const ChatPanel: React.FC<ChatPanelProps> = ({ messages, onSendMessage, isLoading }) => {
  const [inputValue, setInputValue] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [inputValue]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isLoading) return;

    const message = inputValue.trim();
    setInputValue('');
    
    try {
      await onSendMessage(message);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatMessage = (content: string) => {
    // Simple markdown-like formatting
    const lines = content.split('\n');
    return lines.map((line, index) => {
      if (line.startsWith('```')) {
        return null; // Handle code blocks separately
      }
      
      if (line.startsWith('# ')) {
        return <h3 key={index} className="text-lg font-semibold mb-2">{line.slice(2)}</h3>;
      }
      
      if (line.startsWith('## ')) {
        return <h4 key={index} className="text-base font-semibold mb-2">{line.slice(3)}</h4>;
      }
      
      if (line.startsWith('- ')) {
        return <li key={index} className="ml-4 list-disc">{line.slice(2)}</li>;
      }
      
      if (line.trim() === '') {
        return <br key={index} />;
      }
      
      return <p key={index} className="mb-2">{line}</p>;
    });
  };

  const quickActions = [
    { icon: Code, label: 'Explain code', prompt: 'Explain the selected code' },
    { icon: FileText, label: 'Write tests', prompt: 'Write unit tests for this code' },
    { icon: Sparkles, label: 'Optimize', prompt: 'Optimize this code for better performance' },
    { icon: MessageSquare, label: 'Document', prompt: 'Add documentation to this code' },
  ];

  return (
    <div className="h-full flex flex-col bg-[var(--bg-secondary)]">
      {/* Chat Header */}
      <div className="p-3 border-b border-[var(--border-primary)] flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-full bg-[var(--accent-blue)] flex items-center justify-center">
            <Bot className="w-4 h-4 text-white" />
          </div>
          <div>
            <div className="font-medium text-sm">AI Assistant</div>
            <div className="text-xs text-[var(--text-tertiary)]">
              {isLoading ? 'Thinking...' : 'Ready to help'}
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          <button className="btn-ghost p-1.5" title="New conversation">
            <Plus className="w-4 h-4" />
          </button>
          <button className="btn-ghost p-1.5" title="Clear chat">
            <Trash2 className="w-4 h-4" />
          </button>
          <button className="btn-ghost p-1.5" title="Chat settings">
            <Settings className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-3 border-b border-[var(--border-primary)]">
        <div className="text-xs font-medium text-[var(--text-secondary)] mb-2 uppercase tracking-wide">
          Quick Actions
        </div>
        <div className="grid grid-cols-2 gap-2">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={() => setInputValue(action.prompt)}
              className="btn-ghost p-2 text-xs text-left flex items-center gap-2"
            >
              <action.icon className="w-3 h-3" />
              {action.label}
            </button>
          ))}
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-auto p-3 space-y-4">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full text-[var(--text-tertiary)]">
            <div className="text-center">
              <Zap className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">Welcome to AI Assistant</h3>
              <p className="text-sm max-w-sm">
                I'm here to help you code, debug, and learn. Ask me anything about your project!
              </p>
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${message.type === 'user' ? 'flex-row-reverse' : ''}`}
            >
              <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                message.type === 'user' 
                  ? 'bg-[var(--chat-user-bg)]' 
                  : 'bg-[var(--accent-blue)]'
              }`}>
                {message.type === 'user' ? (
                  <User className="w-4 h-4 text-white" />
                ) : (
                  <Bot className="w-4 h-4 text-white" />
                )}
              </div>
              
              <div className={`flex-1 ${message.type === 'user' ? 'text-right' : ''}`}>
                <div className={`chat-message ${message.type} max-w-none`}>
                  <div className="prose prose-sm text-[var(--text-primary)]">
                    {formatMessage(message.content)}
                  </div>
                  
                  <div className="flex items-center justify-between mt-2 pt-2 border-t border-[var(--border-primary)]">
                    <div className="text-xs text-[var(--text-tertiary)]">
                      {message.timestamp.toLocaleTimeString()}
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <button
                        onClick={() => copyToClipboard(message.content)}
                        className="btn-ghost p-1"
                        title="Copy message"
                      >
                        <Copy className="w-3 h-3" />
                      </button>
                      
                      {message.type === 'assistant' && (
                        <button
                          className="btn-ghost p-1"
                          title="Regenerate response"
                        >
                          <RotateCcw className="w-3 h-3" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
        
        {isLoading && (
          <div className="flex gap-3">
            <div className="w-8 h-8 rounded-full bg-[var(--accent-blue)] flex items-center justify-center">
              <Bot className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1">
              <div className="chat-message assistant">
                <div className="ai-thinking">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-3 border-t border-[var(--border-primary)]">
        <form onSubmit={handleSubmit} className="relative">
          <textarea
            ref={textareaRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            onCompositionStart={() => setIsComposing(true)}
            onCompositionEnd={() => setIsComposing(false)}
            placeholder="Ask me anything about your code..."
            className="input resize-none min-h-[44px] max-h-32 pr-12"
            rows={1}
            disabled={isLoading}
          />
          
          <button
            type="submit"
            disabled={!inputValue.trim() || isLoading}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 btn-primary p-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="spinner w-4 h-4" />
            ) : (
              <ArrowUp className="w-4 h-4" />
            )}
          </button>
        </form>
        
        <div className="flex items-center justify-between mt-2 text-xs text-[var(--text-tertiary)]">
          <span>Press Enter to send, Shift+Enter for new line</span>
          <span>{inputValue.length}/2000</span>
        </div>
      </div>
    </div>
  );
};

export default ChatPanel; 