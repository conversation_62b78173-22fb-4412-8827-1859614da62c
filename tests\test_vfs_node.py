"""
FileNode类测试用例

测试虚拟文件系统节点的所有核心功能，包括：
- 节点创建和初始化
- 内容管理
- 子节点管理
- 版本控制
- 元数据管理
"""

import pytest
import tempfile
from datetime import datetime, timedelta
from pathlib import Path

from app.vfs import FileNode, FileType, FilePermission, FileMetadata, FileVersion


class TestFileNodeBasics:
    """测试FileNode基础功能"""
    
    def test_file_node_creation(self):
        """测试文件节点创建"""
        # 创建文件节点
        file_node = FileNode("test.txt", FileType.FILE, "Hello, World!")
        
        assert file_node.name == "test.txt"
        assert file_node.file_type == FileType.FILE
        assert file_node.is_file
        assert not file_node.is_directory
        assert file_node.content == "Hello, World!"
        assert file_node.parent is None
    
    def test_directory_node_creation(self):
        """测试目录节点创建"""
        dir_node = FileNode("test_dir", FileType.DIRECTORY)
        
        assert dir_node.name == "test_dir"
        assert dir_node.file_type == FileType.DIRECTORY
        assert dir_node.is_directory
        assert not dir_node.is_file
        assert dir_node.content is None
        assert len(dir_node.children) == 0
    
    def test_path_generation(self):
        """测试路径生成"""
        # 根节点
        root = FileNode("/", FileType.DIRECTORY)
        assert root.path == "/"
        
        # 子目录
        subdir = FileNode("subdir", FileType.DIRECTORY)
        root.add_child(subdir)
        assert subdir.path == "/subdir"
        
        # 子文件
        file_node = FileNode("test.txt", FileType.FILE)
        subdir.add_child(file_node)
        assert file_node.path == "/subdir/test.txt"


class TestFileContent:
    """测试文件内容管理"""
    
    def test_set_and_get_text_content(self):
        """测试文本内容设置和获取"""
        file_node = FileNode("test.txt", FileType.FILE)
        
        # 设置文本内容
        content = "Hello, World!\n这是测试内容。"
        file_node.set_content(content)
        
        assert file_node.content == content
        assert file_node.metadata.size > 0
        assert file_node.metadata.mime_type is None  # 需要设置扩展名才能自动检测
    
    def test_set_and_get_binary_content(self):
        """测试二进制内容设置和获取"""
        file_node = FileNode("test.bin", FileType.FILE)
        
        # 设置二进制内容
        binary_content = b'\x00\x01\x02\x03\xFF'
        file_node.set_content(binary_content)
        
        assert file_node.content == binary_content
        assert file_node.metadata.size == 5
    
    def test_content_encoding_handling(self):
        """测试内容编码处理"""
        file_node = FileNode("test.txt", FileType.FILE)
        
        # 设置中文内容
        chinese_content = "你好，世界！"
        file_node.set_content(chinese_content)
        
        # 测试不同编码获取
        assert file_node.get_content(encoding="utf-8") == chinese_content
        assert isinstance(file_node.get_content(), bytes)
    
    def test_directory_content_error(self):
        """测试目录节点设置内容时的错误处理"""
        dir_node = FileNode("test_dir", FileType.DIRECTORY)
        
        with pytest.raises(ValueError):
            dir_node.set_content("This should fail")
        
        with pytest.raises(ValueError):
            dir_node.get_content()
    
    def test_mime_type_detection(self):
        """测试MIME类型自动检测"""
        # Python文件
        py_file = FileNode("test.py", FileType.FILE, "print('Hello')")
        assert py_file.metadata.language == "python"
        
        # JavaScript文件
        js_file = FileNode("test.js", FileType.FILE, "console.log('Hello');")
        assert js_file.metadata.language == "javascript"
        
        # HTML文件
        html_file = FileNode("test.html", FileType.FILE, "<html></html>")
        assert html_file.metadata.language == "html"


class TestChildrenManagement:
    """测试子节点管理"""
    
    def test_add_child(self):
        """测试添加子节点"""
        dir_node = FileNode("parent", FileType.DIRECTORY)
        child_file = FileNode("child.txt", FileType.FILE, "content")
        
        dir_node.add_child(child_file)
        
        assert "child.txt" in dir_node.children
        assert dir_node.get_child("child.txt") == child_file
        assert child_file.parent == dir_node
        assert child_file.path == "/parent/child.txt"
    
    def test_add_child_to_file_error(self):
        """测试向文件节点添加子节点的错误处理"""
        file_node = FileNode("test.txt", FileType.FILE)
        child_node = FileNode("child.txt", FileType.FILE)
        
        with pytest.raises(ValueError):
            file_node.add_child(child_node)
    
    def test_duplicate_child_error(self):
        """测试添加重复子节点的错误处理"""
        dir_node = FileNode("parent", FileType.DIRECTORY)
        child1 = FileNode("same_name.txt", FileType.FILE)
        child2 = FileNode("same_name.txt", FileType.FILE)
        
        dir_node.add_child(child1)
        
        with pytest.raises(FileExistsError):
            dir_node.add_child(child2)
    
    def test_remove_child(self):
        """测试移除子节点"""
        dir_node = FileNode("parent", FileType.DIRECTORY)
        child_file = FileNode("child.txt", FileType.FILE)
        
        dir_node.add_child(child_file)
        removed_child = dir_node.remove_child("child.txt")
        
        assert removed_child == child_file
        assert "child.txt" not in dir_node.children
        assert child_file.parent is None
    
    def test_remove_nonexistent_child(self):
        """测试移除不存在的子节点"""
        dir_node = FileNode("parent", FileType.DIRECTORY)
        
        removed_child = dir_node.remove_child("nonexistent.txt")
        assert removed_child is None
    
    def test_list_children(self):
        """测试列出子节点"""
        dir_node = FileNode("parent", FileType.DIRECTORY)
        
        # 添加多个子节点
        file1 = FileNode("b_file.txt", FileType.FILE)
        file2 = FileNode("a_file.txt", FileType.FILE)
        subdir = FileNode("z_subdir", FileType.DIRECTORY)
        hidden_file = FileNode(".hidden", FileType.FILE)
        
        for child in [file1, file2, subdir, hidden_file]:
            dir_node.add_child(child)
        
        # 测试默认列表（不包含隐藏文件，排序）
        children = dir_node.list_children()
        assert len(children) == 3
        assert children[0].name == "z_subdir"  # 目录在前
        assert children[1].name == "a_file.txt"  # 文件按字母序
        assert children[2].name == "b_file.txt"
        
        # 测试包含隐藏文件
        all_children = dir_node.list_children(include_hidden=True)
        assert len(all_children) == 4


class TestVersionControl:
    """测试版本控制功能"""
    
    def test_create_version(self):
        """测试创建版本"""
        file_node = FileNode("test.txt", FileType.FILE, "Initial content")
        
        version_id = file_node.create_version("Initial version")
        
        assert version_id is not None
        assert len(version_id) == 12  # MD5的前12位
        
        versions = file_node.get_versions()
        assert len(versions) == 1
        assert versions[0].version_id == version_id
        assert versions[0].content == "Initial content"
        assert versions[0].comment == "Initial version"
        assert versions[0].checksum != ""
    
    def test_multiple_versions(self):
        """测试多个版本管理"""
        file_node = FileNode("test.txt", FileType.FILE, "Version 1")
        
        # 创建第一个版本
        v1_id = file_node.create_version("First version")
        
        # 修改内容并创建第二个版本
        file_node.set_content("Version 2")
        v2_id = file_node.create_version("Second version")
        
        versions = file_node.get_versions()
        assert len(versions) == 2
        assert versions[0].version_id == v1_id
        assert versions[1].version_id == v2_id
        assert versions[0].content == "Version 1"
        assert versions[1].content == "Version 2"
    
    def test_restore_version(self):
        """测试版本恢复"""
        file_node = FileNode("test.txt", FileType.FILE, "Original content")
        
        # 创建版本
        version_id = file_node.create_version("Backup")
        
        # 修改内容
        file_node.set_content("Modified content")
        assert file_node.content == "Modified content"
        
        # 恢复版本
        success = file_node.restore_version(version_id)
        assert success
        assert file_node.content == "Original content"
    
    def test_restore_nonexistent_version(self):
        """测试恢复不存在的版本"""
        file_node = FileNode("test.txt", FileType.FILE, "content")
        
        success = file_node.restore_version("nonexistent_id")
        assert not success
    
    def test_version_control_directory_error(self):
        """测试目录节点版本控制的错误处理"""
        dir_node = FileNode("test_dir", FileType.DIRECTORY)
        
        with pytest.raises(ValueError):
            dir_node.create_version("This should fail")


class TestMetadata:
    """测试元数据管理"""
    
    def test_update_metadata(self):
        """测试更新元数据"""
        file_node = FileNode("test.txt", FileType.FILE)
        
        # 更新标准字段
        file_node.update_metadata(encoding="gbk", language="text")
        
        assert file_node.metadata.encoding == "gbk"
        assert file_node.metadata.language == "text"
    
    def test_custom_attributes(self):
        """测试自定义属性"""
        file_node = FileNode("test.txt", FileType.FILE)
        
        # 添加自定义属性
        file_node.update_metadata(
            custom_field="custom_value",
            project_id=12345,
            tags=["important", "test"]
        )
        
        assert file_node.metadata.custom_attributes["custom_field"] == "custom_value"
        assert file_node.metadata.custom_attributes["project_id"] == 12345
        assert file_node.metadata.custom_attributes["tags"] == ["important", "test"]
    
    def test_calculate_size(self):
        """测试大小计算"""
        # 文件大小
        file_node = FileNode("test.txt", FileType.FILE, "Hello")
        assert file_node.calculate_size() == len("Hello".encode("utf-8"))
        
        # 目录大小（递归计算）
        dir_node = FileNode("parent", FileType.DIRECTORY)
        file1 = FileNode("file1.txt", FileType.FILE, "content1")
        file2 = FileNode("file2.txt", FileType.FILE, "content2")
        
        dir_node.add_child(file1)
        dir_node.add_child(file2)
        
        expected_size = len("content1".encode("utf-8")) + len("content2".encode("utf-8"))
        assert dir_node.calculate_size() == expected_size


class TestSerialization:
    """测试序列化和反序列化"""
    
    def test_file_to_dict(self):
        """测试文件节点序列化"""
        file_node = FileNode("test.py", FileType.FILE, "print('hello')")
        file_node.update_metadata(custom_field="test_value")
        
        data = file_node.to_dict()
        
        assert data["name"] == "test.py"
        assert data["file_type"] == "file"
        assert data["content"] == "print('hello')"
        assert data["metadata"]["language"] == "python"
        assert data["metadata"]["custom_attributes"]["custom_field"] == "test_value"
    
    def test_directory_to_dict(self):
        """测试目录节点序列化"""
        dir_node = FileNode("parent", FileType.DIRECTORY)
        child_file = FileNode("child.txt", FileType.FILE, "child content")
        dir_node.add_child(child_file)
        
        data = dir_node.to_dict()
        
        assert data["name"] == "parent"
        assert data["file_type"] == "directory"
        assert "children" in data
        assert "child.txt" in data["children"]
        assert data["children"]["child.txt"]["content"] == "child content"
    
    def test_from_dict(self):
        """测试从字典反序列化"""
        # 准备测试数据
        original_file = FileNode("test.py", FileType.FILE, "print('test')")
        original_file.update_metadata(custom_field="test")
        original_file.create_version("test version")
        
        # 序列化
        data = original_file.to_dict()
        
        # 反序列化
        restored_file = FileNode.from_dict(data)
        
        assert restored_file.name == original_file.name
        assert restored_file.file_type == original_file.file_type
        assert restored_file.content == original_file.content
        assert restored_file.metadata.language == original_file.metadata.language
        assert restored_file.metadata.custom_attributes == original_file.metadata.custom_attributes
        assert len(restored_file.get_versions()) == len(original_file.get_versions())
    
    def test_complex_structure_serialization(self):
        """测试复杂结构的序列化"""
        # 创建复杂的目录结构
        root = FileNode("project", FileType.DIRECTORY)
        
        src_dir = FileNode("src", FileType.DIRECTORY)
        root.add_child(src_dir)
        
        main_py = FileNode("main.py", FileType.FILE, "def main(): pass")
        src_dir.add_child(main_py)
        
        readme = FileNode("README.md", FileType.FILE, "# Project")
        root.add_child(readme)
        
        # 序列化和反序列化
        data = root.to_dict()
        restored_root = FileNode.from_dict(data)
        
        # 验证结构
        assert restored_root.name == "project"
        assert len(restored_root.children) == 2
        
        restored_src = restored_root.get_child("src")
        assert restored_src is not None
        assert restored_src.is_directory
        
        restored_main = restored_src.get_child("main.py")
        assert restored_main is not None
        assert restored_main.content == "def main(): pass"
        assert restored_main.path == "/project/src/main.py"


class TestThreadSafety:
    """测试线程安全"""
    
    def test_concurrent_content_access(self):
        """测试并发内容访问"""
        import threading
        import time
        
        file_node = FileNode("test.txt", FileType.FILE, "initial")
        results = []
        errors = []
        
        def reader():
            try:
                for _ in range(100):
                    content = file_node.content
                    results.append(len(content) if content else 0)
                    time.sleep(0.001)
            except Exception as e:
                errors.append(e)
        
        def writer():
            try:
                for i in range(50):
                    file_node.set_content(f"content_{i}" * 10)
                    time.sleep(0.002)
            except Exception as e:
                errors.append(e)
        
        # 启动多个线程
        threads = []
        for _ in range(3):
            threads.append(threading.Thread(target=reader))
        threads.append(threading.Thread(target=writer))
        
        for thread in threads:
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # 检查没有发生错误
        assert len(errors) == 0
        assert len(results) > 0
    
    def test_concurrent_children_management(self):
        """测试并发子节点管理"""
        import threading
        
        dir_node = FileNode("parent", FileType.DIRECTORY)
        errors = []
        
        def add_children(start_idx):
            try:
                for i in range(start_idx, start_idx + 10):
                    child = FileNode(f"child_{i}.txt", FileType.FILE)
                    dir_node.add_child(child)
            except Exception as e:
                errors.append(e)
        
        # 启动多个线程同时添加子节点
        threads = []
        for i in range(0, 50, 10):
            thread = threading.Thread(target=add_children, args=(i,))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # 检查结果
        assert len(errors) == 0
        assert len(dir_node.children) == 50


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 