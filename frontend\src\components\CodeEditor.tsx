import React, { useEffect, useRef, useState } from 'react';
import * as monaco from 'monaco-editor';
import { 
  Save, 
  Undo, 
  Redo, 
  Search, 
  Replace, 
  Settings,
  Copy,
  Scissors,
  Clipboard,
  Code2
} from 'lucide-react';

interface FileTab {
  id: string;
  name: string;
  path: string;
  content: string;
  language: string;
  modified: boolean;
}

interface CodeEditorProps {
  tab: FileTab;
  onContentChange: (content: string) => void;
}

const CodeEditor: React.FC<CodeEditorProps> = ({ tab, onContentChange }) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const monacoRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null);
  const [isReady, setIsReady] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // Initialize Monaco Editor
  useEffect(() => {
    if (!editorRef.current) return;

    // Configure Monaco Editor theme
    monaco.editor.defineTheme('ai-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6a9955' },
        { token: 'keyword', foreground: '569cd6' },
        { token: 'string', foreground: 'ce9178' },
        { token: 'number', foreground: 'b5cea8' },
        { token: 'type', foreground: '4ec9b0' },
        { token: 'function', foreground: 'dcdcaa' },
        { token: 'variable', foreground: '9cdcfe' },
      ],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#cccccc',
        'editor.lineHighlightBackground': '#2d2d30',
        'editor.selectionBackground': '#264f78',
        'editor.inactiveSelectionBackground': '#3a3d41',
        'editorCursor.foreground': '#ffffff',
        'editorLineNumber.foreground': '#858585',
        'editorLineNumber.activeForeground': '#c6c6c6',
        'editorIndentGuide.background': '#404040',
        'editorIndentGuide.activeBackground': '#707070',
        'editorWhitespace.foreground': '#3e3e3e',
        'editorRuler.foreground': '#5a5a5a',
        'scrollbarSlider.background': '#464647',
        'scrollbarSlider.hoverBackground': '#646465',
        'scrollbarSlider.activeBackground': '#bfbfbf',
        'minimap.background': '#1e1e1e',
        'editorBracketMatch.background': '#0064001a',
        'editorBracketMatch.border': '#888888',
      }
    });

    // Create editor instance
    const editor = monaco.editor.create(editorRef.current, {
      value: tab.content,
      language: tab.language,
      theme: 'ai-dark',
      fontSize: 14,
      fontFamily: 'Monaco, Cascadia Code, Consolas, monospace',
      lineHeight: 1.6,
      letterSpacing: 0.5,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      automaticLayout: true,
      contextmenu: true,
      selectOnLineNumbers: true,
      lineNumbers: 'on',
      glyphMargin: true,
      folding: true,
      foldingStrategy: 'indentation',
      showFoldingControls: 'always',
      unfoldOnClickAfterEndOfLine: false,
      renderWhitespace: 'selection',
      renderControlCharacters: false,
             // renderIndentGuides: true, // Deprecated property
      cursorStyle: 'line',
      cursorWidth: 2,
      cursorBlinking: 'smooth',
      smoothScrolling: true,
      mouseWheelZoom: true,
      multiCursorModifier: 'ctrlCmd',
      suggestOnTriggerCharacters: true,
      acceptSuggestionOnEnter: 'on',
      tabCompletion: 'on',
      wordBasedSuggestions: true,
      parameterHints: { enabled: true },
      autoClosingBrackets: 'always',
      autoClosingQuotes: 'always',
      autoSurround: 'languageDefined',
      bracketPairColorization: { enabled: true },
      guides: {
        bracketPairs: true,
        bracketPairsHorizontal: true,
        highlightActiveBracketPair: true,
        indentation: true,
      },
             lightbulb: { enabled: true },
       // codeActionsOnSave: { 'source.fixAll': true }, // Not available in standalone editor
       // formatOnSave: true, // Not available in standalone editor
       // formatOnType: true,
       // formatOnPaste: true,
    });

    monacoRef.current = editor;

    // Add content change listener
    const disposable = editor.onDidChangeModelContent(() => {
      const content = editor.getValue();
      onContentChange(content);
    });

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      // Save functionality
      console.log('Save file:', tab.path);
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyF, () => {
      setShowSearch(true);
    });

    setIsReady(true);

    return () => {
      disposable.dispose();
      editor.dispose();
    };
  }, []);

  // Update editor content when tab changes
  useEffect(() => {
    if (monacoRef.current && tab) {
      const editor = monacoRef.current;
      if (editor.getValue() !== tab.content) {
        editor.setValue(tab.content);
        monaco.editor.setModelLanguage(editor.getModel()!, tab.language);
      }
    }
  }, [tab]);

  const handleUndo = () => {
    monacoRef.current?.trigger('keyboard', 'undo', null);
  };

  const handleRedo = () => {
    monacoRef.current?.trigger('keyboard', 'redo', null);
  };

  const handleSave = () => {
    // Implement save functionality
    console.log('Saving file:', tab.path);
  };

  const handleFind = () => {
    monacoRef.current?.trigger('keyboard', 'actions.find', null);
  };

  const handleReplace = () => {
    monacoRef.current?.trigger('keyboard', 'editor.action.startFindReplaceAction', null);
  };

  const handleCopy = () => {
    monacoRef.current?.trigger('keyboard', 'editor.action.clipboardCopyAction', null);
  };

  const handleCut = () => {
    monacoRef.current?.trigger('keyboard', 'editor.action.clipboardCutAction', null);
  };

  const handlePaste = () => {
    monacoRef.current?.trigger('keyboard', 'editor.action.clipboardPasteAction', null);
  };

  return (
    <div className="h-full flex flex-col bg-[var(--editor-bg)]">
      {/* Editor Toolbar */}
      <div className="h-10 bg-[var(--bg-secondary)] border-b border-[var(--border-primary)] flex items-center justify-between px-3">
        <div className="flex items-center gap-1">
          <button
            onClick={handleUndo}
            className="btn-ghost p-1.5"
            title="Undo (⌘Z)"
          >
            <Undo className="w-4 h-4" />
          </button>
          
          <button
            onClick={handleRedo}
            className="btn-ghost p-1.5"
            title="Redo (⌘⇧Z)"
          >
            <Redo className="w-4 h-4" />
          </button>
          
          <div className="w-px h-6 bg-[var(--border-primary)] mx-2" />
          
          <button
            onClick={handleCopy}
            className="btn-ghost p-1.5"
            title="Copy (⌘C)"
          >
            <Copy className="w-4 h-4" />
          </button>
          
          <button
            onClick={handleCut}
            className="btn-ghost p-1.5"
            title="Cut (⌘X)"
          >
            <Scissors className="w-4 h-4" />
          </button>
          
          <button
            onClick={handlePaste}
            className="btn-ghost p-1.5"
            title="Paste (⌘V)"
          >
            <Clipboard className="w-4 h-4" />
          </button>
          
          <div className="w-px h-6 bg-[var(--border-primary)] mx-2" />
          
          <button
            onClick={handleFind}
            className="btn-ghost p-1.5"
            title="Find (⌘F)"
          >
            <Search className="w-4 h-4" />
          </button>
          
          <button
            onClick={handleReplace}
            className="btn-ghost p-1.5"
            title="Replace (⌘⌥F)"
          >
            <Replace className="w-4 h-4" />
          </button>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-xs text-[var(--text-secondary)]">
            {tab.language}
          </span>
          
          <button
            onClick={handleSave}
            className="btn-primary px-3 py-1 text-xs"
            title="Save (⌘S)"
          >
            <Save className="w-3 h-3 mr-1" />
            Save
          </button>
          
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="btn-ghost p-1.5"
            title="Editor Settings"
          >
            <Settings className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Editor Content */}
      <div className="flex-1 relative">
        {!isReady && (
          <div className="absolute inset-0 flex items-center justify-center bg-[var(--editor-bg)]">
            <div className="text-center">
              <div className="spinner mx-auto mb-4" />
              <p className="text-sm text-[var(--text-secondary)]">Loading editor...</p>
            </div>
          </div>
        )}
        
        <div
          ref={editorRef}
          className="h-full w-full"
          style={{ opacity: isReady ? 1 : 0 }}
        />
      </div>

      {/* Editor Status */}
      <div className="h-6 bg-[var(--bg-secondary)] border-t border-[var(--border-primary)] flex items-center justify-between px-3 text-xs text-[var(--text-secondary)]">
        <div className="flex items-center gap-4">
          <span>Line 1, Column 1</span>
          <span>UTF-8</span>
          <span>LF</span>
        </div>
        
        <div className="flex items-center gap-2">
          {tab.modified && (
            <span className="text-[var(--accent-orange)]">Modified</span>
          )}
          <Code2 className="w-3 h-3" />
        </div>
      </div>
    </div>
  );
};

export default CodeEditor; 