import React, { useState, useEffect, useCallback } from 'react';
import { 
  Search, 
  Settings, 
  MessageSquare, 
  FileText, 
  Folder, 
  Code2, 
  Terminal, 
  GitBranch,
  Play,
  RefreshCw,
  Zap,
  Bot,
  X,
  Plus,
  MoreHorizontal
} from 'lucide-react';

// Import components
import FileExplorer from './components/FileExplorer';
import CodeEditor from './components/CodeEditor';
import ChatPanel from './components/ChatPanel';
import StatusBar from './components/StatusBar';
import CommandPalette from './components/CommandPalette';
import SettingsPanel from './components/SettingsPanel';

interface FileTab {
  id: string;
  name: string;
  path: string;
  content: string;
  language: string;
  modified: boolean;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

const App: React.FC = () => {
  // State management
  const [activeTab, setActiveTab] = useState<string>('');
  const [tabs, setTabs] = useState<FileTab[]>([]);
  const [sidebarWidth, setSidebarWidth] = useState(240);
  const [chatWidth, setChatWidth] = useState(350);
  const [showChat, setShowChat] = useState(true);
  const [showTerminal, setShowTerminal] = useState(false);
  const [showCommandPalette, setShowCommandPalette] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [currentProject, setCurrentProject] = useState('');

  // File operations
  const openFile = useCallback((path: string, name: string, content: string, language: string) => {
    const existingTab = tabs.find(tab => tab.path === path);
    if (existingTab) {
      setActiveTab(existingTab.id);
      return;
    }

    const newTab: FileTab = {
      id: `tab-${Date.now()}`,
      name,
      path,
      content,
      language,
      modified: false
    };

    setTabs(prev => [...prev, newTab]);
    setActiveTab(newTab.id);
  }, [tabs]);

  const closeTab = useCallback((tabId: string) => {
    setTabs(prev => prev.filter(tab => tab.id !== tabId));
    if (activeTab === tabId) {
      const remainingTabs = tabs.filter(tab => tab.id !== tabId);
      setActiveTab(remainingTabs.length > 0 ? remainingTabs[remainingTabs.length - 1].id : '');
    }
  }, [activeTab, tabs]);

  const updateTabContent = useCallback((tabId: string, content: string) => {
    setTabs(prev => prev.map(tab => 
      tab.id === tabId 
        ? { ...tab, content, modified: true }
        : tab
    ));
  }, []);

  // Chat operations
  const sendMessage = useCallback(async (message: string) => {
    const userMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      type: 'user',
      content: message,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      // Simulate API call - replace with actual API integration
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const assistantMessage: ChatMessage = {
        id: `msg-${Date.now() + 1}`,
        type: 'assistant',
        content: `I understand you want to: ${message}. Let me help you with that...`,
        timestamp: new Date()
      };

      setChatMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.metaKey || e.ctrlKey) {
        switch (e.key) {
          case 'k':
            e.preventDefault();
            setShowCommandPalette(true);
            break;
          case 'b':
            e.preventDefault();
            setShowChat(!showChat);
            break;
          case ',':
            e.preventDefault();
            setShowSettings(true);
            break;
          case 'w':
            e.preventDefault();
            if (activeTab) {
              closeTab(activeTab);
            }
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [showChat, activeTab, closeTab]);

  // Check backend connection
  useEffect(() => {
    const checkConnection = async () => {
      try {
        const response = await fetch('http://localhost:8000/health');
        setIsConnected(response.ok);
      } catch {
        setIsConnected(false);
      }
    };

    checkConnection();
    const interval = setInterval(checkConnection, 30000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex flex-col h-screen bg-[var(--bg-primary)] text-[var(--text-primary)]">
      {/* Title Bar */}
      <div className="h-[var(--header-height)] bg-[var(--bg-secondary)] border-b border-[var(--border-primary)] flex items-center justify-between px-4">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Bot className="w-5 h-5 text-[var(--accent-blue)]" />
            <span className="font-semibold">AI Agent IDE</span>
          </div>
          {currentProject && (
            <span className="text-[var(--text-secondary)] text-sm">
              {currentProject}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowCommandPalette(true)}
            className="btn-ghost p-1.5"
            title="Command Palette (⌘K)"
          >
            <Search className="w-4 h-4" />
          </button>
          
          <button
            onClick={() => setShowChat(!showChat)}
            className={`btn-ghost p-1.5 ${showChat ? 'text-[var(--accent-blue)]' : ''}`}
            title="Toggle Chat (⌘B)"
          >
            <MessageSquare className="w-4 h-4" />
          </button>

          <button
            onClick={() => setShowTerminal(!showTerminal)}
            className={`btn-ghost p-1.5 ${showTerminal ? 'text-[var(--accent-blue)]' : ''}`}
            title="Toggle Terminal"
          >
            <Terminal className="w-4 h-4" />
          </button>

          <button
            onClick={() => setShowSettings(true)}
            className="btn-ghost p-1.5"
            title="Settings (⌘,)"
          >
            <Settings className="w-4 h-4" />
          </button>

          <div className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-[var(--accent-green)]' : 'bg-[var(--accent-red)]'}`} />
            <span className="text-xs text-[var(--text-secondary)]">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar - File Explorer */}
        <div 
          className="bg-[var(--bg-secondary)] border-r border-[var(--border-primary)] flex flex-col"
          style={{ width: sidebarWidth }}
        >
          <div className="p-2 border-b border-[var(--border-primary)]">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-[var(--text-secondary)] uppercase tracking-wide">
                Explorer
              </span>
              <div className="flex gap-1">
                <button className="btn-ghost p-1" title="New File">
                  <Plus className="w-3 h-3" />
                </button>
                <button className="btn-ghost p-1" title="Refresh">
                  <RefreshCw className="w-3 h-3" />
                </button>
              </div>
            </div>
          </div>
          
          <div className="flex-1 overflow-auto">
            <FileExplorer onFileOpen={openFile} />
          </div>
        </div>

        {/* Resize Handle */}
        <div 
          className="w-1 bg-[var(--border-primary)] cursor-col-resize hover:bg-[var(--accent-blue)] transition-colors"
          onMouseDown={(e) => {
            const startX = e.clientX;
            const startWidth = sidebarWidth;
            
            const handleMouseMove = (e: MouseEvent) => {
              const newWidth = Math.max(200, Math.min(400, startWidth + e.clientX - startX));
              setSidebarWidth(newWidth);
            };
            
            const handleMouseUp = () => {
              document.removeEventListener('mousemove', handleMouseMove);
              document.removeEventListener('mouseup', handleMouseUp);
            };
            
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
          }}
        />

        {/* Editor Area */}
        <div className="flex-1 flex flex-col">
          {/* Tab Bar */}
          {tabs.length > 0 && (
            <div className="tab-bar">
              {tabs.map(tab => (
                <div
                  key={tab.id}
                  className={`tab ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  <FileText className="w-4 h-4 mr-2" />
                  <span className="truncate">{tab.name}</span>
                  {tab.modified && (
                    <div className="w-2 h-2 rounded-full bg-[var(--accent-orange)] ml-2" />
                  )}
                  <button
                    className="tab-close"
                    onClick={(e) => {
                      e.stopPropagation();
                      closeTab(tab.id);
                    }}
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Editor Content */}
          <div className="flex-1 relative">
            {activeTab ? (
              <CodeEditor
                tab={tabs.find(tab => tab.id === activeTab)!}
                onContentChange={(content) => updateTabContent(activeTab, content)}
              />
            ) : (
              <div className="flex items-center justify-center h-full text-[var(--text-secondary)]">
                <div className="text-center">
                  <Code2 className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <h2 className="text-lg mb-2">Welcome to AI Agent IDE</h2>
                  <p className="text-sm">
                    Open a file from the explorer or use <kbd className="px-2 py-1 bg-[var(--bg-tertiary)] rounded text-xs">⌘K</kbd> to get started
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Terminal Panel */}
          {showTerminal && (
            <>
              <div className="h-1 bg-[var(--border-primary)] cursor-row-resize" />
              <div className="h-48 bg-[var(--bg-primary)] border-t border-[var(--border-primary)]">
                <div className="p-2 bg-[var(--bg-secondary)] border-b border-[var(--border-primary)] flex items-center justify-between">
                  <span className="text-xs font-medium text-[var(--text-secondary)] uppercase tracking-wide">
                    Terminal
                  </span>
                  <button
                    onClick={() => setShowTerminal(false)}
                    className="btn-ghost p-1"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
                <div className="p-3 font-mono text-sm">
                  <div className="text-[var(--accent-green)]">$ </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Chat Panel */}
        {showChat && (
          <>
            {/* Resize Handle */}
            <div 
              className="w-1 bg-[var(--border-primary)] cursor-col-resize hover:bg-[var(--accent-blue)] transition-colors"
              onMouseDown={(e) => {
                const startX = e.clientX;
                const startWidth = chatWidth;
                
                const handleMouseMove = (e: MouseEvent) => {
                  const newWidth = Math.max(300, Math.min(600, startWidth - (e.clientX - startX)));
                  setChatWidth(newWidth);
                };
                
                const handleMouseUp = () => {
                  document.removeEventListener('mousemove', handleMouseMove);
                  document.removeEventListener('mouseup', handleMouseUp);
                };
                
                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
              }}
            />

            {/* Chat Panel */}
            <div 
              className="bg-[var(--bg-secondary)] border-l border-[var(--border-primary)] flex flex-col"
              style={{ width: chatWidth }}
            >
              <div className="p-3 border-b border-[var(--border-primary)] flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-[var(--accent-blue)]" />
                  <span className="font-medium">AI Assistant</span>
                </div>
                <button
                  onClick={() => setShowChat(false)}
                  className="btn-ghost p-1"
                  title="Close Chat"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
              
              <ChatPanel
                messages={chatMessages}
                onSendMessage={sendMessage}
                isLoading={isLoading}
              />
            </div>
          </>
        )}
      </div>

      {/* Status Bar */}
      <StatusBar 
        isConnected={isConnected}
        activeTab={tabs.find(tab => tab.id === activeTab)}
        totalTabs={tabs.length}
      />

      {/* Command Palette */}
      {showCommandPalette && (
        <CommandPalette
          onClose={() => setShowCommandPalette(false)}
          onCommand={(command) => {
            console.log('Command:', command);
            setShowCommandPalette(false);
          }}
        />
      )}

      {/* Settings Panel */}
      {showSettings && (
        <SettingsPanel onClose={() => setShowSettings(false)} />
      )}
    </div>
  );
};

export default App;