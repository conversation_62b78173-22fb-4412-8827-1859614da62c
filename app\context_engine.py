"""
代码上下文引擎 (Context Engine)

基于tree-sitter的代码解析和语义搜索引擎，为AI编程工具提供：
- 代码结构分析和符号提取
- 语义向量化和相似度搜索
- 智能上下文组装和优化
- 跨语言代码理解

主要组件：
- CodeParser: 代码解析器
- SemanticSearcher: 语义搜索引擎
- ContextBuilder: 上下文构建器
- SymbolExtractor: 符号提取器
"""

import os
import re
import ast
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Union, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import threading
import asyncio
from concurrent.futures import ThreadPoolExecutor

try:
    import tree_sitter
    from tree_sitter import Language, Parser, Node
    import tree_sitter_python
    import tree_sitter_javascript
    import tree_sitter_typescript
except ImportError:
    print("Warning: tree-sitter not available. Code parsing will be limited.")
    tree_sitter = None

try:
    from sentence_transformers import SentenceTransformer
    import faiss
    import numpy as np
except ImportError:
    print("Warning: sentence-transformers or faiss not available. Semantic search disabled.")
    SentenceTransformer = None
    faiss = None

from .vfs import VirtualFileSystem, FileNode, FileType


class LanguageType(Enum):
    """支持的编程语言类型"""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JSX = "jsx"
    TSX = "tsx"
    JSON = "json"
    MARKDOWN = "markdown"
    UNKNOWN = "unknown"


class SymbolType(Enum):
    """代码符号类型"""
    FUNCTION = "function"
    CLASS = "class"
    METHOD = "method"
    VARIABLE = "variable"
    CONSTANT = "constant"
    IMPORT = "import"
    EXPORT = "export"
    INTERFACE = "interface"
    ENUM = "enum"
    TYPE = "type"


@dataclass
class CodeSymbol:
    """代码符号信息"""
    name: str
    symbol_type: SymbolType
    language: LanguageType
    file_path: str
    line_start: int
    line_end: int
    column_start: int
    column_end: int
    definition: str
    documentation: Optional[str] = None
    parent_symbol: Optional[str] = None
    children: List[str] = field(default_factory=list)
    references: List[Tuple[str, int, int]] = field(default_factory=list)
    embedding: Optional[np.ndarray] = None


@dataclass
class CodeContext:
    """代码上下文信息"""
    primary_file: str
    primary_symbols: List[CodeSymbol]
    related_files: List[str]
    related_symbols: List[CodeSymbol]
    imports: List[str]
    exports: List[str]
    dependencies: Dict[str, List[str]]
    context_score: float = 0.0


class CodeParser:
    """代码解析器"""
    
    def __init__(self):
        """初始化代码解析器"""
        self._parsers: Dict[LanguageType, Optional[Parser]] = {}
        self._languages: Dict[LanguageType, Optional[Language]] = {}
        self._init_tree_sitter()
        
        # 文件扩展名到语言类型的映射
        self._ext_to_lang = {
            '.py': LanguageType.PYTHON,
            '.js': LanguageType.JAVASCRIPT,
            '.jsx': LanguageType.JSX,
            '.ts': LanguageType.TYPESCRIPT,
            '.tsx': LanguageType.TSX,
            '.json': LanguageType.JSON,
            '.md': LanguageType.MARKDOWN,
        }
        
        # 线程安全锁
        self._lock = threading.RLock()
    
    def _init_tree_sitter(self):
        """初始化tree-sitter语言解析器"""
        if tree_sitter is None:
            return
        
        try:
            # 初始化支持的语言
            lang_configs = {
                LanguageType.PYTHON: tree_sitter_python,
                LanguageType.JAVASCRIPT: tree_sitter_javascript,
                LanguageType.TYPESCRIPT: tree_sitter_typescript,
            }
            
            for lang_type, lang_module in lang_configs.items():
                try:
                    language = Language(lang_module.language(), lang_type.value)
                    parser = Parser()
                    parser.set_language(language)
                    
                    self._languages[lang_type] = language
                    self._parsers[lang_type] = parser
                except Exception as e:
                    print(f"Failed to initialize {lang_type.value} parser: {e}")
                    self._languages[lang_type] = None
                    self._parsers[lang_type] = None
        
        except Exception as e:
            print(f"Failed to initialize tree-sitter: {e}")
    
    def detect_language(self, file_path: str, content: Optional[str] = None) -> LanguageType:
        """检测文件的编程语言类型"""
        # 基于文件扩展名检测
        path = Path(file_path)
        ext = path.suffix.lower()
        
        if ext in self._ext_to_lang:
            return self._ext_to_lang[ext]
        
        # 基于内容检测
        if content:
            content_lower = content.lower()
            
            # Python特征检测
            if re.search(r'def\s+\w+\s*\(|class\s+\w+\s*\(|import\s+\w+', content):
                return LanguageType.PYTHON
            
            # JavaScript/TypeScript特征检测
            if re.search(r'function\s+\w+\s*\(|const\s+\w+\s*=|let\s+\w+\s*=', content):
                if re.search(r':\s*\w+\s*[=;]|interface\s+\w+', content):
                    return LanguageType.TYPESCRIPT
                return LanguageType.JAVASCRIPT
        
        return LanguageType.UNKNOWN
    
    def parse_file(self, file_path: str, content: str) -> Optional[Node]:
        """解析文件内容，返回AST根节点"""
        language = self.detect_language(file_path, content)
        
        if language == LanguageType.UNKNOWN or language not in self._parsers:
            return None
        
        parser = self._parsers[language]
        if parser is None:
            return None
        
        try:
            with self._lock:
                tree = parser.parse(content.encode('utf-8'))
                return tree.root_node
        except Exception as e:
            print(f"Failed to parse {file_path}: {e}")
            return None
    
    def extract_symbols(self, file_path: str, content: str) -> List[CodeSymbol]:
        """从文件中提取代码符号"""
        language = self.detect_language(file_path, content)
        root_node = self.parse_file(file_path, content)
        
        if root_node is None:
            # 回退到正则表达式解析
            return self._extract_symbols_regex(file_path, content, language)
        
        # 使用tree-sitter解析
        return self._extract_symbols_tree_sitter(file_path, content, root_node, language)
    
    def _extract_symbols_tree_sitter(
        self, 
        file_path: str, 
        content: str, 
        root_node: Node, 
        language: LanguageType
    ) -> List[CodeSymbol]:
        """使用tree-sitter提取符号"""
        symbols = []
        content_lines = content.split('\n')
        
        def traverse_node(node: Node, parent_name: Optional[str] = None):
            """递归遍历AST节点"""
            node_type = node.type
            
            # Python符号提取
            if language == LanguageType.PYTHON:
                if node_type == 'function_definition':
                    func_name = self._get_node_text(node, content, 'identifier')
                    if func_name:
                        symbol = self._create_symbol(
                            func_name, SymbolType.FUNCTION, language, file_path,
                            node, content_lines, parent_name
                        )
                        symbols.append(symbol)
                        parent_name = func_name
                
                elif node_type == 'class_definition':
                    class_name = self._get_node_text(node, content, 'identifier')
                    if class_name:
                        symbol = self._create_symbol(
                            class_name, SymbolType.CLASS, language, file_path,
                            node, content_lines, parent_name
                        )
                        symbols.append(symbol)
                        parent_name = class_name
                
                elif node_type == 'import_statement' or node_type == 'import_from_statement':
                    import_text = node.text.decode('utf-8')
                    symbol = self._create_symbol(
                        import_text, SymbolType.IMPORT, language, file_path,
                        node, content_lines, parent_name
                    )
                    symbols.append(symbol)
            
            # JavaScript/TypeScript符号提取
            elif language in [LanguageType.JAVASCRIPT, LanguageType.TYPESCRIPT]:
                if node_type == 'function_declaration':
                    func_name = self._get_node_text(node, content, 'identifier')
                    if func_name:
                        symbol = self._create_symbol(
                            func_name, SymbolType.FUNCTION, language, file_path,
                            node, content_lines, parent_name
                        )
                        symbols.append(symbol)
                
                elif node_type == 'class_declaration':
                    class_name = self._get_node_text(node, content, 'identifier')
                    if class_name:
                        symbol = self._create_symbol(
                            class_name, SymbolType.CLASS, language, file_path,
                            node, content_lines, parent_name
                        )
                        symbols.append(symbol)
                        parent_name = class_name
                
                elif node_type == 'interface_declaration' and language == LanguageType.TYPESCRIPT:
                    interface_name = self._get_node_text(node, content, 'type_identifier')
                    if interface_name:
                        symbol = self._create_symbol(
                            interface_name, SymbolType.INTERFACE, language, file_path,
                            node, content_lines, parent_name
                        )
                        symbols.append(symbol)
            
            # 递归处理子节点
            for child in node.children:
                traverse_node(child, parent_name)
        
        traverse_node(root_node)
        return symbols
    
    def _extract_symbols_regex(
        self, 
        file_path: str, 
        content: str, 
        language: LanguageType
    ) -> List[CodeSymbol]:
        """使用正则表达式提取符号（备用方案）"""
        symbols = []
        lines = content.split('\n')
        
        if language == LanguageType.PYTHON:
            # Python函数和类的正则表达式
            func_pattern = r'^(\s*)def\s+(\w+)\s*\('
            class_pattern = r'^(\s*)class\s+(\w+)\s*\(?'
            import_pattern = r'^import\s+[\w\.,\s]+|^from\s+[\w\.]+\s+import\s+[\w\.,\s\*]+'
            
            for i, line in enumerate(lines):
                # 函数定义
                func_match = re.match(func_pattern, line)
                if func_match:
                    func_name = func_match.group(2)
                    symbol = CodeSymbol(
                        name=func_name,
                        symbol_type=SymbolType.FUNCTION,
                        language=language,
                        file_path=file_path,
                        line_start=i + 1,
                        line_end=i + 1,
                        column_start=func_match.start(2),
                        column_end=func_match.end(2),
                        definition=line.strip()
                    )
                    symbols.append(symbol)
                
                # 类定义
                class_match = re.match(class_pattern, line)
                if class_match:
                    class_name = class_match.group(2)
                    symbol = CodeSymbol(
                        name=class_name,
                        symbol_type=SymbolType.CLASS,
                        language=language,
                        file_path=file_path,
                        line_start=i + 1,
                        line_end=i + 1,
                        column_start=class_match.start(2),
                        column_end=class_match.end(2),
                        definition=line.strip()
                    )
                    symbols.append(symbol)
                
                # 导入语句
                import_match = re.match(import_pattern, line)
                if import_match:
                    symbol = CodeSymbol(
                        name=line.strip(),
                        symbol_type=SymbolType.IMPORT,
                        language=language,
                        file_path=file_path,
                        line_start=i + 1,
                        line_end=i + 1,
                        column_start=0,
                        column_end=len(line),
                        definition=line.strip()
                    )
                    symbols.append(symbol)
        
        return symbols
    
    def _get_node_text(self, node: Node, content: str, child_type: str) -> Optional[str]:
        """获取指定类型子节点的文本"""
        for child in node.children:
            if child.type == child_type:
                return child.text.decode('utf-8')
        return None
    
    def _create_symbol(
        self,
        name: str,
        symbol_type: SymbolType,
        language: LanguageType,
        file_path: str,
        node: Node,
        content_lines: List[str],
        parent_name: Optional[str] = None
    ) -> CodeSymbol:
        """创建代码符号对象"""
        start_point = node.start_point
        end_point = node.end_point
        
        # 提取完整定义
        definition_lines = content_lines[start_point[0]:min(end_point[0] + 1, len(content_lines))]
        definition = '\n'.join(definition_lines)
        
        # 提取文档字符串
        documentation = self._extract_documentation(node, content_lines)
        
        return CodeSymbol(
            name=name,
            symbol_type=symbol_type,
            language=language,
            file_path=file_path,
            line_start=start_point[0] + 1,
            line_end=end_point[0] + 1,
            column_start=start_point[1],
            column_end=end_point[1],
            definition=definition,
            documentation=documentation,
            parent_symbol=parent_name
        )
    
    def _extract_documentation(self, node: Node, content_lines: List[str]) -> Optional[str]:
        """提取函数/类的文档字符串"""
        # 简单实现：查找第一个字符串字面量
        for child in node.children:
            if child.type == 'string' and child.start_point[0] == node.start_point[0] + 1:
                doc_text = child.text.decode('utf-8')
                # 移除引号
                if doc_text.startswith('"""') or doc_text.startswith("'''"):
                    return doc_text[3:-3].strip()
                elif doc_text.startswith('"') or doc_text.startswith("'"):
                    return doc_text[1:-1].strip()
        return None


class SemanticSearcher:
    """语义搜索引擎"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        """初始化语义搜索引擎"""
        self.model_name = model_name
        self.model: Optional[SentenceTransformer] = None
        self.index: Optional[faiss.Index] = None
        self.symbols: List[CodeSymbol] = []
        self.embeddings: Optional[np.ndarray] = None
        
        # 线程池用于异步处理
        self.executor = ThreadPoolExecutor(max_workers=4)
        self._lock = threading.RLock()
        
        self._init_model()
    
    def _init_model(self):
        """初始化语言模型"""
        if SentenceTransformer is None or faiss is None:
            print("Warning: Required dependencies not available for semantic search")
            return
        
        try:
            self.model = SentenceTransformer(self.model_name)
            print(f"Semantic search model '{self.model_name}' initialized successfully")
        except Exception as e:
            print(f"Failed to initialize semantic search model: {e}")
    
    def build_index(self, symbols: List[CodeSymbol]):
        """构建符号的语义搜索索引"""
        if self.model is None:
            return
        
        with self._lock:
            self.symbols = symbols
            
            # 生成文本描述用于向量化
            texts = []
            for symbol in symbols:
                text_parts = [
                    f"{symbol.symbol_type.value}: {symbol.name}",
                    f"in {symbol.file_path}",
                ]
                
                if symbol.documentation:
                    text_parts.append(symbol.documentation)
                elif symbol.definition:
                    # 使用定义的前几行
                    definition_lines = symbol.definition.split('\n')[:3]
                    text_parts.append(' '.join(definition_lines))
                
                texts.append(' '.join(text_parts))
            
            # 生成向量
            try:
                embeddings = self.model.encode(texts, convert_to_numpy=True)
                self.embeddings = embeddings
                
                # 构建FAISS索引
                dimension = embeddings.shape[1]
                self.index = faiss.IndexFlatIP(dimension)  # 内积相似度
                
                # 归一化向量以便使用余弦相似度
                faiss.normalize_L2(embeddings)
                self.index.add(embeddings)
                
                print(f"Built semantic search index with {len(symbols)} symbols")
                
            except Exception as e:
                print(f"Failed to build semantic search index: {e}")
    
    def search(
        self, 
        query: str, 
        top_k: int = 10, 
        min_score: float = 0.5
    ) -> List[Tuple[CodeSymbol, float]]:
        """语义搜索代码符号"""
        if self.model is None or self.index is None:
            return []
        
        try:
            # 向量化查询
            query_embedding = self.model.encode([query], convert_to_numpy=True)
            faiss.normalize_L2(query_embedding)
            
            # 搜索
            scores, indices = self.index.search(query_embedding, min(top_k, len(self.symbols)))
            
            # 过滤结果
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if score >= min_score and idx < len(self.symbols):
                    results.append((self.symbols[idx], float(score)))
            
            return results
            
        except Exception as e:
            print(f"Semantic search failed: {e}")
            return []
    
    def find_similar_symbols(
        self, 
        symbol: CodeSymbol, 
        top_k: int = 5
    ) -> List[Tuple[CodeSymbol, float]]:
        """查找相似的代码符号"""
        if symbol.embedding is not None:
            # 使用已有的向量
            embedding = symbol.embedding.reshape(1, -1)
        else:
            # 生成向量
            text = f"{symbol.symbol_type.value}: {symbol.name} {symbol.definition}"
            embedding = self.model.encode([text], convert_to_numpy=True)
        
        faiss.normalize_L2(embedding)
        
        try:
            scores, indices = self.index.search(embedding, top_k + 1)  # +1 to exclude self
            
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx < len(self.symbols) and self.symbols[idx] != symbol:
                    results.append((self.symbols[idx], float(score)))
            
            return results[:top_k]
            
        except Exception as e:
            print(f"Similar symbol search failed: {e}")
            return []


class ContextBuilder:
    """上下文构建器"""
    
    def __init__(self, vfs: VirtualFileSystem, parser: CodeParser, searcher: SemanticSearcher):
        """初始化上下文构建器"""
        self.vfs = vfs
        self.parser = parser
        self.searcher = searcher
        self._symbol_cache: Dict[str, List[CodeSymbol]] = {}
        self._dependency_graph: Dict[str, Set[str]] = {}
        self._lock = threading.RLock()
    
    def build_context(
        self, 
        query: str, 
        focus_files: List[str] = None,
        max_files: int = 10,
        max_symbols: int = 50
    ) -> CodeContext:
        """构建代码上下文"""
        
        # 1. 语义搜索相关符号
        semantic_results = self.searcher.search(query, top_k=max_symbols // 2)
        
        # 2. 确定主要文件
        primary_files = set()
        if focus_files:
            primary_files.update(focus_files)
        
        for symbol, score in semantic_results[:10]:  # 取前10个高分符号的文件
            primary_files.add(symbol.file_path)
        
        primary_files = list(primary_files)[:max_files // 2]
        
        # 3. 收集主要符号
        primary_symbols = []
        for symbol, score in semantic_results:
            if symbol.file_path in primary_files:
                primary_symbols.append(symbol)
        
        # 4. 分析依赖关系
        related_files = set()
        for file_path in primary_files:
            deps = self._get_file_dependencies(file_path)
            related_files.update(deps)
        
        related_files = list(related_files - set(primary_files))[:max_files // 2]
        
        # 5. 收集相关符号
        related_symbols = []
        for file_path in related_files:
            file_symbols = self._get_file_symbols(file_path)
            related_symbols.extend(file_symbols[:5])  # 每个文件最多5个符号
        
        # 6. 提取导入导出信息
        imports, exports = self._extract_imports_exports(primary_files + related_files)
        
        # 7. 构建依赖图
        dependencies = {}
        for file_path in primary_files + related_files:
            dependencies[file_path] = list(self._get_file_dependencies(file_path))
        
        # 8. 计算上下文相关性得分
        context_score = self._calculate_context_score(query, primary_symbols, related_symbols)
        
        return CodeContext(
            primary_file=primary_files[0] if primary_files else "",
            primary_symbols=primary_symbols,
            related_files=related_files,
            related_symbols=related_symbols,
            imports=imports,
            exports=exports,
            dependencies=dependencies,
            context_score=context_score
        )
    
    def _get_file_symbols(self, file_path: str) -> List[CodeSymbol]:
        """获取文件的符号信息（带缓存）"""
        with self._lock:
            if file_path in self._symbol_cache:
                return self._symbol_cache[file_path]
            
            try:
                content = self.vfs.read_file(file_path, encoding='utf-8')
                if isinstance(content, bytes):
                    content = content.decode('utf-8')
                
                symbols = self.parser.extract_symbols(file_path, content)
                self._symbol_cache[file_path] = symbols
                return symbols
                
            except Exception as e:
                print(f"Failed to extract symbols from {file_path}: {e}")
                return []
    
    def _get_file_dependencies(self, file_path: str) -> Set[str]:
        """获取文件的依赖关系"""
        with self._lock:
            if file_path in self._dependency_graph:
                return self._dependency_graph[file_path]
            
            dependencies = set()
            
            try:
                content = self.vfs.read_file(file_path, encoding='utf-8')
                if isinstance(content, bytes):
                    content = content.decode('utf-8')
                
                language = self.parser.detect_language(file_path, content)
                
                # 提取导入依赖
                if language == LanguageType.PYTHON:
                    import_pattern = r'(?:from\s+([\w\.]+)\s+import|import\s+([\w\.]+))'
                    for match in re.finditer(import_pattern, content):
                        module = match.group(1) or match.group(2)
                        if module and not module.startswith('.'):
                            # 尝试解析为相对路径
                            dep_path = self._resolve_import_path(file_path, module, language)
                            if dep_path:
                                dependencies.add(dep_path)
                
                elif language in [LanguageType.JAVASCRIPT, LanguageType.TYPESCRIPT]:
                    import_pattern = r'(?:import.*?from\s+[\'"]([^\'"]+)[\'"]|require\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\))'
                    for match in re.finditer(import_pattern, content):
                        module = match.group(1) or match.group(2)
                        if module and not module.startswith('node_modules'):
                            dep_path = self._resolve_import_path(file_path, module, language)
                            if dep_path:
                                dependencies.add(dep_path)
                
                self._dependency_graph[file_path] = dependencies
                return dependencies
                
            except Exception as e:
                print(f"Failed to analyze dependencies for {file_path}: {e}")
                return set()
    
    def _resolve_import_path(self, current_file: str, import_path: str, language: LanguageType) -> Optional[str]:
        """解析导入路径为实际文件路径"""
        current_dir = str(Path(current_file).parent)
        
        # 处理相对导入
        if import_path.startswith('.'):
            if language == LanguageType.PYTHON:
                # Python相对导入
                rel_path = import_path.replace('.', '/') + '.py'
                full_path = str(Path(current_dir) / rel_path)
            else:
                # JS/TS相对导入
                if not import_path.endswith(('.js', '.ts', '.jsx', '.tsx')):
                    # 尝试不同扩展名
                    for ext in ['.js', '.ts', '.jsx', '.tsx', '/index.js', '/index.ts']:
                        test_path = str(Path(current_dir) / (import_path + ext))
                        if self.vfs.exists(test_path):
                            return test_path
                full_path = str(Path(current_dir) / import_path)
        else:
            # 绝对导入（简化处理）
            if language == LanguageType.PYTHON:
                full_path = import_path.replace('.', '/') + '.py'
            else:
                full_path = import_path
        
        # 检查文件是否存在
        if self.vfs.exists(full_path):
            return full_path
        
        return None
    
    def _extract_imports_exports(self, files: List[str]) -> Tuple[List[str], List[str]]:
        """提取导入导出信息"""
        imports = []
        exports = []
        
        for file_path in files:
            symbols = self._get_file_symbols(file_path)
            for symbol in symbols:
                if symbol.symbol_type == SymbolType.IMPORT:
                    imports.append(symbol.definition)
                elif symbol.symbol_type == SymbolType.EXPORT:
                    exports.append(symbol.definition)
        
        return imports, exports
    
    def _calculate_context_score(
        self, 
        query: str, 
        primary_symbols: List[CodeSymbol], 
        related_symbols: List[CodeSymbol]
    ) -> float:
        """计算上下文相关性得分"""
        if not primary_symbols:
            return 0.0
        
        # 简单评分：基于符号数量和查询关键词匹配
        query_lower = query.lower()
        score = 0.0
        
        # 主要符号得分
        for symbol in primary_symbols:
            if query_lower in symbol.name.lower():
                score += 1.0
            if symbol.documentation and query_lower in symbol.documentation.lower():
                score += 0.5
            if query_lower in symbol.definition.lower():
                score += 0.3
        
        # 相关符号得分
        for symbol in related_symbols:
            if query_lower in symbol.name.lower():
                score += 0.3
            if symbol.documentation and query_lower in symbol.documentation.lower():
                score += 0.1
        
        # 归一化得分
        total_symbols = len(primary_symbols) + len(related_symbols)
        if total_symbols > 0:
            score = min(score / total_symbols, 1.0)
        
        return score 