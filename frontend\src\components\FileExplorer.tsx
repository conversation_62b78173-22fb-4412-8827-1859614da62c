import React, { useState, useEffect, useRef } from 'react';
import {
  Folder,
  Folder<PERSON><PERSON>,
  FileText,
  ChevronRight,
  ChevronDown,
  Plus,
  MoreVertical,
  Trash2,
  Edit3,
  Co<PERSON>,
  RefreshCw
} from 'lucide-react';

interface FileNode {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  modified?: string;
  children?: FileNode[];
}

interface FileExplorerProps {
  onFileOpen: (path: string, name: string, content: string, language: string) => void;
}

interface ContextMenuProps {
  x: number;
  y: number;
  node: FileNode;
  onClose: () => void;
  onAction: (action: string, node: FileNode) => void;
}

const ContextMenu: React.FC<ContextMenuProps> = ({ x, y, node, onClose, onAction }) => {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  return (
    <div
      ref={menuRef}
      className="context-menu fixed"
      style={{ left: x, top: y }}
    >
      <div className="context-menu-item" onClick={() => onAction('new-file', node)}>
        <FileText className="w-4 h-4 mr-2" />
        New File
      </div>
      <div className="context-menu-item" onClick={() => onAction('new-folder', node)}>
        <Folder className="w-4 h-4 mr-2" />
        New Folder
      </div>
      <div className="context-menu-separator" />
      <div className="context-menu-item" onClick={() => onAction('copy', node)}>
        <Copy className="w-4 h-4 mr-2" />
        Copy
      </div>
      <div className="context-menu-item" onClick={() => onAction('rename', node)}>
        <Edit3 className="w-4 h-4 mr-2" />
        Rename
      </div>
      <div className="context-menu-item" onClick={() => onAction('delete', node)}>
        <Trash2 className="w-4 h-4 mr-2" />
        Delete
      </div>
    </div>
  );
};

const FileExplorer: React.FC<FileExplorerProps> = ({ onFileOpen }) => {
  const [fileTree, setFileTree] = useState<FileNode[]>([]);
  const [expandedDirs, setExpandedDirs] = useState<Set<string>>(new Set());
  const [selectedPath, setSelectedPath] = useState<string>('');
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    node: FileNode;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Mock file tree data - replace with API call
  useEffect(() => {
    loadFileTree();
  }, []);

  const loadFileTree = async () => {
    setIsLoading(true);
    try {
      // Mock data - replace with actual API call
      const mockTree: FileNode[] = [
        {
          name: 'src',
          path: '/src',
          type: 'directory',
          children: [
            {
              name: 'components',
              path: '/src/components',
              type: 'directory',
              children: [
                { name: 'App.tsx', path: '/src/components/App.tsx', type: 'file', size: 1024 },
                { name: 'Header.tsx', path: '/src/components/Header.tsx', type: 'file', size: 512 }
              ]
            },
            { name: 'index.ts', path: '/src/index.ts', type: 'file', size: 256 },
            { name: 'main.tsx', path: '/src/main.tsx', type: 'file', size: 400 }
          ]
        },
        {
          name: 'public',
          path: '/public',
          type: 'directory',
          children: [
            { name: 'index.html', path: '/public/index.html', type: 'file', size: 800 }
          ]
        },
        { name: 'package.json', path: '/package.json', type: 'file', size: 1200 },
        { name: 'README.md', path: '/README.md', type: 'file', size: 600 }
      ];
      
      setFileTree(mockTree);
      // Auto-expand root directories
      setExpandedDirs(new Set(['/src', '/public']));
    } catch (error) {
      console.error('Failed to load file tree:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleDirectory = (path: string) => {
    const newExpanded = new Set(expandedDirs);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedDirs(newExpanded);
  };

  const handleFileClick = async (node: FileNode) => {
    if (node.type === 'directory') {
      toggleDirectory(node.path);
      return;
    }

    setSelectedPath(node.path);
    
    try {
      // Mock file content - replace with actual API call
      const mockContent = `// ${node.name}\n// File content would be loaded from API\nconsole.log('Hello from ${node.name}');`;
      const language = getLanguageFromExtension(node.name);
      
      onFileOpen(node.path, node.name, mockContent, language);
    } catch (error) {
      console.error('Failed to load file:', error);
    }
  };

  const handleRightClick = (e: React.MouseEvent, node: FileNode) => {
    e.preventDefault();
    setContextMenu({
      x: e.clientX,
      y: e.clientY,
      node
    });
  };

  const handleContextAction = (action: string, node: FileNode) => {
    setContextMenu(null);
    
    switch (action) {
      case 'new-file':
        console.log('Create new file in:', node.path);
        break;
      case 'new-folder':
        console.log('Create new folder in:', node.path);
        break;
      case 'copy':
        console.log('Copy:', node.path);
        break;
      case 'rename':
        console.log('Rename:', node.path);
        break;
      case 'delete':
        console.log('Delete:', node.path);
        break;
    }
  };

  const getLanguageFromExtension = (filename: string): string => {
    const ext = filename.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'json': 'json',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'md': 'markdown',
      'yml': 'yaml',
      'yaml': 'yaml'
    };
    
    return languageMap[ext || ''] || 'plaintext';
  };

  const getFileIcon = (node: FileNode) => {
    if (node.type === 'directory') {
      return expandedDirs.has(node.path) ? 
        <FolderOpen className="w-4 h-4 text-[var(--accent-blue)]" /> :
        <Folder className="w-4 h-4 text-[var(--accent-blue)]" />;
    }
    
    return <FileText className="w-4 h-4 text-[var(--text-secondary)]" />;
  };

  const renderFileTree = (nodes: FileNode[], depth = 0) => {
    return nodes.map((node) => (
      <div key={node.path}>
        <div
          className={`tree-item ${selectedPath === node.path ? 'selected' : ''}`}
          style={{ paddingLeft: `${depth * 12 + 8}px` }}
          onClick={() => handleFileClick(node)}
          onContextMenu={(e) => handleRightClick(e, node)}
        >
          {node.type === 'directory' && (
            <div className="w-4 h-4 mr-1 flex items-center justify-center">
              {expandedDirs.has(node.path) ? 
                <ChevronDown className="w-3 h-3" /> : 
                <ChevronRight className="w-3 h-3" />
              }
            </div>
          )}
          {node.type === 'file' && <div className="w-4 h-4 mr-1" />}
          
          {getFileIcon(node)}
          
          <span className="ml-2 truncate">{node.name}</span>
          
          {node.size && (
            <span className="ml-auto text-xs text-[var(--text-tertiary)]">
              {formatFileSize(node.size)}
            </span>
          )}
        </div>
        
        {node.type === 'directory' && expandedDirs.has(node.path) && node.children && (
          renderFileTree(node.children, depth + 1)
        )}
      </div>
    ));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes}B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
  };

  return (
    <div className="h-full flex flex-col">
      {isLoading ? (
        <div className="flex items-center justify-center h-32">
          <div className="spinner" />
        </div>
      ) : fileTree.length === 0 ? (
        <div className="flex items-center justify-center h-32 text-[var(--text-tertiary)]">
          <div className="text-center">
            <Folder className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No files found</p>
          </div>
        </div>
      ) : (
        <div className="flex-1 overflow-auto py-1">
          {renderFileTree(fileTree)}
        </div>
      )}

      {contextMenu && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          node={contextMenu.node}
          onClose={() => setContextMenu(null)}
          onAction={handleContextAction}
        />
      )}
    </div>
  );
};

export default FileExplorer; 